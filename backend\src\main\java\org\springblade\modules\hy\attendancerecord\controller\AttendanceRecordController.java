/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.attendancerecord.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.attendancerecord.pojo.entity.AttendanceRecordEntity;
import org.springblade.modules.hy.attendancerecord.pojo.vo.AttendanceRecordVO;
import org.springblade.modules.hy.attendancerecord.excel.AttendanceRecordExcel;
import org.springblade.modules.hy.attendancerecord.wrapper.AttendanceRecordWrapper;
import org.springblade.modules.hy.attendancerecord.service.IAttendanceRecordService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 参会签到记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-attendancerecord/attendanceRecord")
@Tag(name = "参会签到记录表", description = "参会签到记录表接口")
public class AttendanceRecordController extends BladeController {

	private final IAttendanceRecordService attendanceRecordService;

	/**
	 * 参会签到记录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入attendanceRecord")
	public R<AttendanceRecordVO> detail(AttendanceRecordEntity attendanceRecord) {
		AttendanceRecordEntity detail = attendanceRecordService.getOne(Condition.getQueryWrapper(attendanceRecord));
		return R.data(AttendanceRecordWrapper.build().entityVO(detail));
	}
	/**
	 * 参会签到记录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入attendanceRecord")
	public R<IPage<AttendanceRecordVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> attendanceRecord, Query query) {
		IPage<AttendanceRecordEntity> pages = attendanceRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(attendanceRecord, AttendanceRecordEntity.class));
		return R.data(AttendanceRecordWrapper.build().pageVO(pages));
	}

	/**
	 * 参会签到记录表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入attendanceRecord")
	public R<IPage<AttendanceRecordVO>> page(AttendanceRecordVO attendanceRecord, Query query) {
		IPage<AttendanceRecordVO> pages = attendanceRecordService.selectAttendanceRecordPage(Condition.getPage(query), attendanceRecord);
		return R.data(pages);
	}

	/**
	 * 参会签到记录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入attendanceRecord")
	public R save(@Valid @RequestBody AttendanceRecordEntity attendanceRecord) {
		return R.status(attendanceRecordService.save(attendanceRecord));
	}

	/**
	 * 参会签到记录表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入attendanceRecord")
	public R update(@Valid @RequestBody AttendanceRecordEntity attendanceRecord) {
		return R.status(attendanceRecordService.updateById(attendanceRecord));
	}

	/**
	 * 参会签到记录表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入attendanceRecord")
	public R submit(@Valid @RequestBody AttendanceRecordEntity attendanceRecord) {
		return R.status(attendanceRecordService.saveOrUpdate(attendanceRecord));
	}

	/**
	 * 参会签到记录表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(attendanceRecordService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-attendanceRecord")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入attendanceRecord")
	public void exportAttendanceRecord(@Parameter(hidden = true) @RequestParam Map<String, Object> attendanceRecord, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<AttendanceRecordEntity> queryWrapper = Condition.getQueryWrapper(attendanceRecord, AttendanceRecordEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(AttendanceRecord::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(AttendanceRecordEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<AttendanceRecordExcel> list = attendanceRecordService.exportAttendanceRecord(queryWrapper);
		ExcelUtil.export(response, "参会签到记录表数据" + DateUtil.time(), "参会签到记录表数据表", list, AttendanceRecordExcel.class);
	}

}
