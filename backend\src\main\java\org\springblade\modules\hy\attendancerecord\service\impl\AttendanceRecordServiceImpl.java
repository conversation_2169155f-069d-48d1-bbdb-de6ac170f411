/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.attendancerecord.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.hy.attendancerecord.pojo.entity.AttendanceRecordEntity;
import org.springblade.modules.hy.attendancerecord.pojo.vo.AttendanceRecordVO;
import org.springblade.modules.hy.attendancerecord.excel.AttendanceRecordExcel;
import org.springblade.modules.hy.attendancerecord.mapper.AttendanceRecordMapper;
import org.springblade.modules.hy.attendancerecord.service.IAttendanceRecordService;
import org.springblade.modules.hy.checkin.pojo.dto.QRCodeResponseDTO;
import org.springblade.modules.hy.checkin.utils.QRCodeUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 参会签到记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Slf4j
@Service
public class AttendanceRecordServiceImpl extends BaseServiceImpl<AttendanceRecordMapper, AttendanceRecordEntity> implements IAttendanceRecordService {

	/**
	 * 移动端签到页面基础URL
	 */
	@Value("${ding-app.qrcode.mobile-base-url}")
	private String mobileBaseUrl;

	@Override
	public IPage<AttendanceRecordVO> selectAttendanceRecordPage(IPage<AttendanceRecordVO> page, AttendanceRecordVO attendanceRecord) {
		return page.setRecords(baseMapper.selectAttendanceRecordPage(page, attendanceRecord));
	}

	@Override
	public QRCodeResponseDTO generateAgendaQRCode(Long agendaId) {
		// 生成议程签到码（不包含用户ID）
		String checkinCode = QRCodeUtil.generateAgendaCheckinCode(agendaId);

		// 生成二维码内容（包含跳转URL）
		String qrContent = String.format("%s/mobile/checkin?agendaId=%d", mobileBaseUrl, agendaId);
		String qrCodeData = QRCodeUtil.generateQRCodeBase64(qrContent);

		// 设置过期时间（24小时后，议程级别的二维码有效期更长）
		LocalDateTime expireTime = LocalDateTime.now().plusHours(24);

		// 构建响应
		QRCodeResponseDTO response = new QRCodeResponseDTO();
		response.setQrCodeData(qrCodeData);
		response.setCheckinCode(checkinCode);
		response.setExpireTime(expireTime);
		response.setUserId(null); // 议程级别二维码不绑定用户
		response.setAgendaId(agendaId);

		log.info("为议程 {} 生成签到二维码，签到码：{}", agendaId, checkinCode);

		return response;
	}

	@Override
	public AttendanceRecordEntity checkinByAgenda(Long agendaId, Long userId) {
		// 检查是否已经签到
		if (isCheckedIn(userId, agendaId)) {
			return null; // 返回null表示已经签到过了
		}

		// 创建签到记录
		AttendanceRecordEntity record = new AttendanceRecordEntity();
		record.setUserId(userId);
		record.setHyAgendaId(agendaId);  // 转换为Long存储
		record.setCheckinTime(LocalDateTime.now());
		record.setStatusText("已签到");

		// 保存签到记录
		boolean saved = save(record);
		if (!saved) {
			return null; // 返回null表示保存失败
		}

		log.info("用户 {} 议程 {} 签到成功", userId, agendaId);

		return record;
	}

	@Override
	public AttendanceRecordEntity getCheckinStatus(Long userId, Long agendaId) {
		if (agendaId != null) {
			// 查询特定议程的签到记录
			return baseMapper.selectByUserIdAndAgendaId(userId, agendaId);
		} else {
			// 查询今日签到记录
			return baseMapper.selectTodayRecordByUserId(userId);  // 直接使用Long类型
		}
	}

	@Override
	public boolean isCheckedIn(Long userId, Long agendaId) {
		LambdaQueryWrapper<AttendanceRecordEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(AttendanceRecordEntity::getUserId, userId);  // 直接使用Long类型，不转换

		if (agendaId != null) {
			// 检查特定议程的签到状态
			wrapper.eq(AttendanceRecordEntity::getHyAgendaId, agendaId);
		} else {
			// 检查今日签到状态
			LocalDate today = LocalDate.now();
			wrapper.ge(AttendanceRecordEntity::getCheckinTime, today.atStartOfDay());
			wrapper.lt(AttendanceRecordEntity::getCheckinTime, today.plusDays(1).atStartOfDay());
		}

		return count(wrapper) > 0;
	}


	@Override
	public List<AttendanceRecordExcel> exportAttendanceRecord(Wrapper<AttendanceRecordEntity> queryWrapper) {
		List<AttendanceRecordExcel> attendanceRecordList = baseMapper.exportAttendanceRecord(queryWrapper);
		//attendanceRecordList.forEach(attendanceRecord -> {
		//	attendanceRecord.setTypeName(DictCache.getValue(DictEnum.YES_NO, AttendanceRecord.getType()));
		//});
		return attendanceRecordList;
	}

}
