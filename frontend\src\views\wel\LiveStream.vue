<template>
  <div class="page-container">
    <!-- 页面内容 -->
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-broadcast-tower"></i>
          <h2>{{ mainTitle || "直播现场" }}</h2>
          <p>{{ subTitle || "数智攀登，管理跃升 - 企业管理现场会直播" }}</p>
        </div>

        <!-- 加载状态 -->
        <LoadingIndicator
          :show="isLoading"
          text="正在加载直播信息..."
          size="small"
        />

        <!-- 错误提示 -->
        <ErrorMessage
          :show="hasError && !isLoading"
          type="warning"
          :message="errorMessage"
          :show-retry="true"
          :retrying="isLoading"
          @retry="refreshData"
          @close="clearError"
        />

        <!-- 直播内容滚动容器 -->
        <div class="live-scroll-container">
          <!-- 直播状态 -->
          <div class="live-status">
            <div class="list-item status-card" id="liveStatus">
              <div class="status-indicator">
                <div class="live-dot" :class="{ offline: !isLive }"></div>
                <span id="statusText">{{ statusText }}</span>
              </div>
              <!-- <div class="viewer-count">
                <i class="fas fa-eye"></i>
                <span id="viewerCount">{{ viewerCount }}</span> 人观看
              </div> -->
            </div>
          </div>

          <!-- 直播窗口 -->
          <div class="live-player list-item">
            <div class="player-container" id="playerContainer">
              <div class="live-player-content" @click="openLiveStream">
                <i class="fas fa-play-circle"></i>
                <h3>点击观看直播</h3>
                <p>{{ liveUrl ? '点击此处观看直播' : '暂无直播链接' }}</p>
                <!-- <div class="play-overlay">
                  <i class="fas fa-external-link-alt"></i>
                  <span>在新窗口中打开</span>
                </div> -->
              </div>
            </div>
            <div class="player-controls">
              <button class="control-btn" id="playBtn" @click="togglePlay">
                <i :class="isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
              </button>
              <button class="control-btn" @click="toggleMute">
                <i
                  :class="isMuted ? 'fas fa-volume-mute' : 'fas fa-volume-up'"
                  id="volumeIcon"
                ></i>
              </button>
              <button class="control-btn" @click="toggleFullscreen">
                <i class="fas fa-expand"></i>
              </button>
              <button class="control-btn" @click="shareStream">
                <i class="fas fa-share"></i>
              </button>
            </div>
          </div>

          <!-- 直播信息 -->
          <div class="live-info">
            <h3 class="section-title">
              <i class="fas fa-info-circle"></i>
              直播信息
            </h3>
            <div class="info-grid list-item">
              <div class="info-item">
                <i class="fas fa-calendar"></i>
                <div>
                  <strong>直播时间</strong>
                  <p>{{ formattedLiveTime }}</p>
                </div>
              </div>

              <div class="info-item">
                <i class="fas fa-microphone"></i>
                <div>
                  <strong>当前议程</strong>
                  <p id="currentAgenda">{{ currentAgenda }}</p>
                </div>
              </div>

              <div class="info-item">
                <i class="fas fa-user"></i>
                <div>
                  <strong>主讲人</strong>
                  <p id="currentSpeaker">{{ currentSpeaker }}</p>
                </div>
              </div>

              <!-- <div class="info-item">
                <i class="fas fa-signal"></i>
                <div>
                  <strong>直播质量</strong>
                  <p>
                    <select
                      id="qualitySelect"
                      @change="changeQuality"
                      v-model="selectedQuality"
                    >
                      <option value="auto">自动</option>
                      <option value="1080p">1080P 高清</option>
                      <option value="720p">720P 标清</option>
                      <option value="480p">480P 流畅</option>
                    </select>
                  </p>
                </div>
              </div> -->
            </div>
          </div>

          <!-- 互动区域 -->
          <!-- <div class="interaction-area">
            <h3 class="section-title">
              <i class="fas fa-comments"></i>
              互动交流
            </h3>
            <div class="chat-section list-item">
              <div class="chat-messages" id="chatMessages" ref="chatMessages">
                <div class="chat-message system">
                  <span class="message-text">欢迎观看企业管理现场会直播！</span>
                </div>
                <div
                  v-for="(message, index) in chatMessages"
                  :key="index"
                  class="chat-message user"
                >
                  <strong>{{ message.user }}：</strong> {{ message.text }}
                </div>
              </div>
              <div class="chat-input">
                <input
                  type="text"
                  id="messageInput"
                  v-model="newMessage"
                  placeholder="输入您的评论..."
                  @keypress="handleKeyPress"
                />
                <button @click="sendMessage">
                  <i class="fas fa-paper-plane"></i>
                </button>
              </div>
            </div>

            <div class="interaction-stats list-item">
              <div class="stat-item">
                <button
                  class="like-btn"
                  :class="{ liked: hasLiked }"
                  @click="likeStream"
                >
                  <i class="fas fa-thumbs-up"></i>
                  <span id="likeCount">{{ likeCount }}</span>
                </button>
              </div>
              <div class="stat-item">
                <span
                  ><i class="fas fa-comments"></i>
                  <span id="commentCount">{{ commentCount }}</span> 条评论</span
                >
              </div>
            </div>
          </div> -->
        </div>

        <!-- <div class="download-section">
          <button class="submit-btn" @click="downloadRecording" v-if="!isLive">
            <i class="fas fa-download"></i>
            下载直播回放
          </button>
          <button class="submit-btn" @click="remindLater" v-if="isLive">
            <i class="fas fa-bell"></i>
            直播结束提醒我
          </button>
        </div> -->
      </div>
    </main>

    <!-- 底部导航栏占位符 -->
    <div class="bottom-nav-placeholder"></div>
  </div>
</template>

<script>
import { getList } from "@/api/livestream/liveStream";
import { dataTransformers } from "@/utils/apiHelper";
import apiMixin from "@/mixins/apiMixin";
import LoadingIndicator from "@/components/LoadingIndicator.vue";
import ErrorMessage from "@/components/ErrorMessage.vue";
import { getDictionary } from "@/api/system/dictbiz";
import * as dd from 'dingtalk-jsapi';

export default {
  name: "LiveStream",
  mixins: [apiMixin],
  components: {
    LoadingIndicator,
    ErrorMessage,
  },
  data() {
    return {
      mainTitle: "",
      subTitle: "",
      liveUrl: "https://player.bilibili.com/player.html?aid=123456",
      isLive: false,
      isPlaying: false,
      isMuted: false,
      hasLiked: false,
      likeCount: 0,
      commentCount: 0,
      viewerCount: 0,
      statusText: "即将开始",
      currentAgenda: "开幕式致辞",
      currentSpeaker: "公司领导",
      selectedQuality: "auto",
      newMessage: "",
      chatMessages: [],
      dataSource: "unknown",
      responseTime: 0,
      // 直播时间相关
      startTime: "",
      endTime: "",
    };
  },
  computed: {
    /**
     * 格式化直播时间显示
     * 将开始时间和结束时间合并显示，如：15号5:00-6:00
     */
    formattedLiveTime() {
      if (!this.startTime || !this.endTime) {
        return "2025年9月15日 09:00 - 17:00"; // 默认时间
      }

      try {
        const startDate = new Date(this.startTime);
        const endDate = new Date(this.endTime);

        // 检查日期是否有效
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return "2025年9月15日 09:00 - 17:00"; // 默认时间
        }
        // 格式化开始时间
        const startDay = startDate.getMonth() + 1 + "月" + startDate.getDate();
        const startHour = startDate.getHours().toString().padStart(2, "0");
        const startMinute = startDate.getMinutes().toString().padStart(2, "0");

        // 格式化结束时间
        const endDay = endDate.getMonth() + 1 + "月" + endDate.getDate();
        const endHour = endDate.getHours().toString().padStart(2, "0");
        const endMinute = endDate.getMinutes().toString().padStart(2, "0");

        // 如果是同一天，显示格式：15号5:00-6:00
        if (startDay === endDay) {
          return `${startDay}号 ${startHour}:${startMinute} - ${endHour}:${endMinute}`;
        } else {
          // 如果不是同一天，显示完整格式
          return `${startDay}号 ${startHour}:${startMinute} - ${endDay}号 ${endHour}:${endMinute}`;
        }
      } catch (error) {
        console.error("时间格式化错误:", error);
        return "2025年9月15日 09:00 - 17:00"; // 默认时间
      }
    },
  },
  async mounted() {
    await this.loadLiveStreamData();
    await this.loadData();
    // 初始化
    this.updateViewerCount();
    setInterval(this.updateViewerCount, 30000); // 每30秒更新一次观看人数

    // 模拟自动消息
    // setTimeout(() => {
    //   this.chatMessages.push({
    //     user: "张经理",
    //     text: "演讲内容很精彩！",
    //   });
    //   this.commentCount++;
    // }, 5000);


  },

  methods: {
    async loadData() {
      try {
        const response = await getDictionary({
          code: "hy_live_stream", // 字典编码，需要在后台配置
        });
        // 检查响应格式
        if (response && response.data && response.data.success) {
          const dictData = response.data.data;
          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 从字典数据中提取文本
            this.mainTitle = dictData.find(
              (item) => item.dictValue === "主标题"
            )?.dictKey;
            this.subTitle = dictData.find(
              (item) => item.dictValue === "副标题"
            )?.dictKey;
          } else {
            console.log("API返回数据为空");
          }
        } else {
          throw new Error("API响应格式不正确");
        }
      } catch (error) {
        console.error("加载标题数据失败:", error);
        // 保持默认标题
      }
    },
    /**
     * 加载直播数据
     */
    async loadLiveStreamData() {
      const startTime = Date.now();

      try {
        console.log("开始加载直播数据...");

        // 直接调用API
        const response = await getList(1, 20, {});
        console.log("直播API响应:", response);

        // 检查响应格式
        if (response && response.data && response.data.success) {
          // 使用数据转换器处理数据
          const transformedData = dataTransformers.liveStream(response.data);
          console.log("转换后的直播数据:", transformedData);

          if (transformedData && transformedData.length > 0) {
            const liveInfo = transformedData[0];
            this.liveUrl = liveInfo.url || this.liveUrl;
            this.statusText =
              liveInfo.statusText ||
              (liveInfo.status === "live" ? "直播中" : "即将开始");
            this.isLive = liveInfo.status === "live";
            this.viewerCount = liveInfo.viewerCount || 0;
            this.currentAgenda = liveInfo.title || this.currentAgenda;
            this.currentSpeaker = liveInfo.speaker || this.currentSpeaker;
            // 读取开始和结束时间
            this.startTime = liveInfo.startTime || "";
            this.endTime = liveInfo.endTime || "";


          }

          this.dataSource = "api";
          this.hasError = false;
          this.errorMessage = "";
        } else {
          throw new Error("API响应格式不正确");
        }

        this.responseTime = Date.now() - startTime;

      } catch (error) {
        console.error("加载直播数据失败:", error);

        // 使用默认数据
        if (this.defaultLiveData && this.defaultLiveData.length > 0) {
          const liveInfo = this.defaultLiveData[0];
          this.liveUrl = liveInfo.url || this.liveUrl;
          this.statusText = liveInfo.status === "live" ? "直播中" : "即将开始";
          this.isLive = liveInfo.status === "live";
          this.viewerCount = liveInfo.viewerCount || 0;
          this.currentAgenda = liveInfo.title || this.currentAgenda;
          this.currentSpeaker = liveInfo.speaker || this.currentSpeaker;
          // 读取默认的开始和结束时间
          this.startTime = liveInfo.startTime || "";
          this.endTime = liveInfo.endTime || "";


        }

        this.dataSource = "fallback";
        this.hasError = true;
        this.errorMessage = error.message || "数据加载失败，使用默认数据";
        this.responseTime = Date.now() - startTime;
      }
    },

    /**
     * 刷新直播数据
     */
    async refreshData() {
      await this.loadLiveStreamData();
    },

    /**
     * 格式化直播数据
     */
    formatApiData(data, type) {
      if (type === "array" && Array.isArray(data)) {
        return dataTransformers.liveStream(data);
      }
      return data;
    },
    togglePlay() {
      // 直接打开视频链接
      if (this.liveUrl) {
        this.openLiveStream();
      } else {
        // 如果没有视频链接，显示提示
        if (this.$message) {
          this.$message.warning('暂无视频链接');
        } else {
          alert('暂无视频链接');
        }
      }
    },
    toggleMute() {
      this.isMuted = !this.isMuted;
    },
    async toggleFullscreen() {
      // 直接使用 openLink 打开视频，提供更好的全屏体验
      if (this.liveUrl) {
        this.openLiveStream();
      } else {
        if (this.$message) {
          this.$message.warning('暂无视频链接');
        } else {
          alert('暂无视频链接');
        }
      }
    },

    shareStream() {
      if (navigator.share) {
        navigator.share({
          title: "企业管理现场会直播",
          text: "正在观看企业管理现场会直播",
          url: window.location.href,
        });
      } else {
        // Fallback: 复制链接到剪贴板
        navigator.clipboard
          .writeText(window.location.href)
          .then(() => {
            alert("直播链接已复制到剪贴板！");
          })
          .catch(() => {
            alert("无法使用分享功能，请手动复制链接：" + window.location.href);
          });
      }
    },
    // changeQuality() {
    //   alert(`切换到${this.selectedQuality}画质\n（演示功能）`);
    // },
    sendMessage() {
      const message = this.newMessage.trim();

      if (message) {
        this.chatMessages.push({
          user: "我",
          text: message,
        });

        this.newMessage = "";
        this.commentCount++;

        // 滚动到底部
        this.$nextTick(() => {
          const chatMessages = this.$refs.chatMessages;
          if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
          }
        });
      }
    },
    handleKeyPress(event) {
      if (event.key === "Enter") {
        this.sendMessage();
      }
    },
    likeStream() {
      if (!this.hasLiked) {
        this.hasLiked = true;
        this.likeCount++;
      }
    },
    updateViewerCount() {
      this.viewerCount = Math.floor(Math.random() * 50) + 100;
    },
    // 新增方法：下载回放
    downloadRecording() {
      alert("直播回放下载功能\n（演示功能）\n回放文件将开始下载");
    },
    // 新增方法：提醒功能
    remindLater() {
      alert("已设置直播结束提醒\n（演示功能）\n直播结束时将通知您");
    },

    /**
     * 使用钉钉 openLink 打开视频
     */
    openLiveStream() {
      if (!this.liveUrl) {
        console.warn('视频链接为空，无法打开视频');
        if (this.$message) {
          this.$message.warning('暂无视频链接');
        } else {
          alert('暂无视频链接');
        }
        return;
      }

      console.log('准备自动打开视频:', this.liveUrl);

      dd.biz.util.openLink({
        url: this.liveUrl, // 要打开的视频链接地址
        onSuccess: (result) => {
          console.log('钉钉 openLink 成功，视频已自动打开:', result);
        },
        onFail: (err) => {
          if (this.$message) {
            this.$message.error('打开直播失败，请稍后重试');
          } else {
            alert('打开直播失败，请稍后重试');
          }        }
      });
    },
  },
};
</script>

<style scoped>
/* 页面通用样式*/
.page-container {
  width: 100%;
  min-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 页面内容 */
.page-content {
  margin-top: 15px;
}

/* 容器样式 */
.list-container {
  position: relative;
  border-radius: 15px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
    0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
  max-height: 85vh;
  overflow: hidden;
}

/* 磨砂背景 */
.list-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url(/img/bg08.jpg) no-repeat 0/cover fixed;
  filter: blur(20px); /* 只模糊背景 */
  z-index: 1; /* 背景层在底层 */
}

/* 内容层（保持清晰） */
.list-container > * {
  position: relative;
  z-index: 2; /* 内容层在背景层上方，不受模糊影响 */
}

/* 标题区域样式统一 */
.form-header {
  text-align: center;
  margin-bottom: 10px;
  color: #ffffff;
}

.form-header i {
  font-size: 36px;
  color: #07d3f0;
  margin-bottom: 10px;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.form-header h2 {
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 10px;
}

/* 直播内容滚动容器 - 与议程页面滚动容器样式统一 */
.live-scroll-container {
  max-height: calc(85vh - 150px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: 5px;
  margin-bottom: 10px;
}

/* 滚动条美化 - 与议程页面统一 */
.live-scroll-container::-webkit-scrollbar {
  width: 5px;
}

.live-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.live-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
}

.live-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
}

/* 列表项样式 */
.list-item {
  background: rgba(255, 255, 255, 0.06);
  /* backdrop-filter: blur(10px); */
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
  position: relative;
  overflow: hidden;
}

/* 卡片悬停效果 */
.list-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.list-item:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.list-item:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

/* 直播状态区域 */
.live-status {
  margin: 10px 0;
}

.status-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
}

.live-dot {
  width: 12px;
  height: 12px;
  background: #07d3f0;
  border-radius: 50%;
  animation: pulse 2s infinite;
  box-shadow: 0 0 10px rgba(7, 211, 240, 0.6);
}

.live-dot.offline {
  background: rgba(255, 255, 255, 0.3);
  animation: none;
  box-shadow: none;
}

@keyframes pulse {
  0% {
    opacity: 1;
    box-shadow: 0 0 10px rgba(7, 211, 240, 0.6);
  }
  50% {
    opacity: 0.5;
    box-shadow: 0 0 20px rgba(7, 211, 240, 0.8);
  }
  100% {
    opacity: 1;
    box-shadow: 0 0 10px rgba(7, 211, 240, 0.6);
  }
}

.status-indicator span {
  color: #ffffff;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.viewer-count {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.viewer-count i {
  color: #07d3f0;
}

/* 直播播放器区域 */
.live-player {
  margin: 20px 0;
  overflow: hidden;
}

.player-container {
  width: 100%;
  height: 200px;
  background: rgba(0, 0, 0, 0.7);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin-bottom: 15px;
}

.player-placeholder {
  text-align: center;
  color: white;
  padding: 20px;
}

.player-placeholder i {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.7;
  color: #07d3f0;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.player-placeholder h3 {
  margin-bottom: 10px;
  font-size: 18px;
  color: white;
}

.player-placeholder p {
  font-size: 14px;
  opacity: 0.8;
  color: rgba(255, 255, 255, 0.9);
}

/* 直播播放内容区域 */
.live-player-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.1), rgba(7, 211, 240, 0.05));
  border: 2px dashed rgba(7, 211, 240, 0.3);
  border-radius: 8px;
}

.live-player-content:hover {
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.2), rgba(7, 211, 240, 0.1));
  border-color: rgba(7, 211, 240, 0.5);
  transform: scale(1.02);
}

.live-player-content i.fas.fa-play-circle {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.8;
  color: #07d3f0;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.live-player-content h3 {
  margin-bottom: 10px;
  font-size: 18px;
  color: white;
}

.live-player-content p {
  font-size: 14px;
  opacity: 0.8;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15px;
}

.play-overlay {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(7, 211, 240, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  color: #07d3f0;
  border: 1px solid rgba(7, 211, 240, 0.3);
}

.play-overlay i {
  font-size: 14px;
}

.player-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.control-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(7, 211, 240, 0.3);
  color: #07d3f0;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.control-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.control-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.control-btn:hover {
  background: rgba(7, 211, 240, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(7, 211, 240, 0.2);
}

/* 直播信息区域 */
.live-info {
  margin: 30px 0;
}

/* 区域标题样式 - 与议程页面日期标题样式统一 */
.section-title {
  background: linear-gradient(
    135deg,
    rgba(7, 211, 240, 0.3),
    rgba(7, 211, 240, 0.15)
  );
  color: white;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-size: 17px;
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid rgba(7, 211, 240, 0.2);
  /* backdrop-filter: blur(8px); */
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  padding: 20px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.info-item i {
  color: #07d3f0;
  font-size: 18px;
  width: 20px;
  margin-top: 3px;
  text-shadow: 0 0 10px rgba(7, 211, 240, 0.5);
}

.info-item strong {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  display: block;
  margin-bottom: 3px;
}

.info-item p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  margin: 0;
}

#qualitySelect {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 5px;
  padding: 3px 8px;
  font-size: 12px;
  color: #07d3f0;
  outline: none;
}

#qualitySelect option {
  background: rgba(0, 0, 0, 0.7);
  color: rgba(255, 255, 255, 0.8);
}

/* 互动区域 */
.interaction-area {
  margin: 30px 0;
}

.message-text {
  color: white;
}

.chat-section {
  overflow: hidden;
}

.chat-messages {
  height: 150px;
  overflow-y: auto;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px 8px 0 0;
  margin-bottom: 15px;
}

/* 聊天区域滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 5px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
}

.chat-message {
  margin-bottom: 10px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 13px;
  animation: slideInUp 0.3s ease forwards;
}

.chat-message.system {
  background: rgba(7, 211, 240, 0.15);
  color: #07d3f0;
  text-align: center;
  border: 1px solid rgba(7, 211, 240, 0.2);
}

.chat-message.user {
  background: rgba(255, 255, 255, 0.08);
  border-left: 3px solid #07d3f0;
  color: rgba(255, 255, 255, 0.9);
}

.chat-message.user strong {
  color: #07d3f0;
}

.chat-input {
  display: flex;
  gap: 10px;
}

.chat-input input {
  flex: 1;
  padding: 10px;
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  background: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.9);
}

.chat-input input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.chat-input button {
  background: #07d3f0;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.chat-input button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.chat-input button:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-input button:hover {
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(7, 211, 240, 0.3);
}

.interaction-stats {
  display: flex;
  justify-content: space-around;
  padding: 15px;
}

.like-btn {
  background: none;
  border: none;
  color: #07d3f0;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  padding: 5px 10px;
  border-radius: 20px;
}

.like-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.like-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.like-btn:hover {
  background: rgba(7, 211, 240, 0.1);
  transform: translateY(-2px);
}

.like-btn.liked {
  color: #ff6b6b;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.stat-item i {
  color: #07d3f0;
}

/* 下载区域样式 - 与议程页面统一 */
.download-section {
  margin-top: 25px;
  text-align: center;
}

/* 提交按钮 - 与议程页面统一 */
.submit-btn {
  width: 100%;
  background: linear-gradient(
    135deg,
    rgba(7, 211, 240, 0.3),
    rgba(7, 211, 240, 0.15)
  );
  color: white;
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 10px;
  padding: 14px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  /* backdrop-filter: blur(8px); */
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.submit-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(7, 211, 240, 0.2);
  background: linear-gradient(
    135deg,
    rgba(7, 211, 240, 0.4),
    rgba(7, 211, 240, 0.2)
  );
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* 底部导航栏占位符 - 确保10%高度 */
.bottom-nav-placeholder {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 10vh; /* 10%视窗高度 */
  z-index: 10; /* 确保在内容上方 */
  pointer-events: none; /* 允许点击穿透到下方内容 */
}

/* 动画效果统一 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 为直播项添加交错动画 */
.list-item:nth-child(1) {
  animation-delay: 0.1s;
}
.list-item:nth-child(2) {
  animation-delay: 0.2s;
}
.list-item:nth-child(3) {
  animation-delay: 0.3s;
}
.list-item:nth-child(4) {
  animation-delay: 0.4s;
}
.list-item:nth-child(5) {
  animation-delay: 0.5s;
}
</style>