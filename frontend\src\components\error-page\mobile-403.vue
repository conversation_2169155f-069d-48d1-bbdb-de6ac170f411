<template>
  <div class="mobile-error-page">
    
    <!-- 错误信息 -->
    <div class="error-content">
      <h1>403</h1>
      <div class="error-title">账号未授权</div>
      <div class="error-desc">
        抱歉，您的钉钉账号暂未获得系统访问权限。
        <br>
        请联系管理员为您开通权限。
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="error-actions">
      <button class="retry-btn" @click="retryLogin">
        <i class="fas fa-redo"></i>
        重新登录
      </button>
      <!-- <button class="contact-btn" @click="contactAdmin">
        <i class="fas fa-phone"></i>
        联系管理员
      </button> -->
    </div>
    
    <!-- 帮助信息 -->
    <div class="help-info">
      <p>如果您认为这是一个错误，请：</p>
      <ul>
        <li>确认您使用的是正确的钉钉账号</li>
        <li>联系系统管理员申请权限</li>
        <li>稍后重试登录</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// 重新登录
const retryLogin = () => {
  router.push('/dinglogin')
}

// 联系管理员（这里可以根据实际需求实现）
const contactAdmin = () => {
  // 可以打开联系方式弹窗或跳转到联系页面
  alert('请联系系统管理员开通权限\n\n联系方式：\n电话：xxx-xxxx-xxxx\n邮箱：<EMAIL>')
}
</script>

<style scoped>
.mobile-error-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  text-align: center;
}

.error-icon {
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
}

.error-icon i {
  font-size: 48px;
  color: #ff6b6b;
}

.error-content {
  margin-bottom: 40px;
}

.error-content h1 {
  font-size: 72px;
  font-weight: bold;
  color: #ff6b6b;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.error-desc {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  max-width: 300px;
  margin: 0 auto;
}

.error-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 40px;
  width: 100%;
  max-width: 280px;
}

.retry-btn,
.contact-btn {
  width: 100%;
  height: 50px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.retry-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
}

.contact-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3);
}

.retry-btn:active,
.contact-btn:active {
  transform: translateY(0);
}

.help-info {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 20px;
  max-width: 320px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.help-info p {
  font-size: 14px;
  color: #333;
  margin: 0 0 15px 0;
  font-weight: 500;
}

.help-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.help-info li {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
  line-height: 1.4;
}

.help-info li::before {
  content: '•';
  color: #4CAF50;
  font-weight: bold;
  position: absolute;
  left: 0;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .mobile-error-page {
    padding: 15px;
  }
  
  .error-icon {
    width: 100px;
    height: 100px;
  }
  
  .error-icon i {
    font-size: 40px;
  }
  
  .error-content h1 {
    font-size: 60px;
  }
  
  .error-title {
    font-size: 20px;
  }
  
  .error-desc {
    font-size: 14px;
  }
  
  .retry-btn,
  .contact-btn {
    height: 45px;
    font-size: 15px;
  }
}

@media (max-width: 360px) {
  .error-content h1 {
    font-size: 48px;
  }
  
  .error-title {
    font-size: 18px;
  }
  
  .help-info {
    padding: 15px;
  }
}
</style>
