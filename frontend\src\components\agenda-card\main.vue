<template>
  <div class="content-column">
    <div class="scroll-container" ref="container">
      <h3 class="scroll-text" ref="textElement">{{ currentDescription || text }}</h3>
    </div>
  </div>
</template>

<script>
import {getDictionary} from "@/api/system/dictbiz";
import {getList} from "@/api/agenda/agenda";
import {dataTransformers} from "@/utils/apiHelper";
import {getToken} from "@/utils/auth";
import NProgress from "nprogress";
import {exportBlob} from "@/api/common";
import {downloadXls} from "@/utils/util";
import {dateNow} from "@/utils/date";

export default {
  data(){
    return {
      text:"当前议程",
      subTitle:"",
      agendaList: [],
      currentDescription: "",
      defaultAgendaData: [],
      agendaTimer: null,
      // 添加防抖动标记和计数
      lastCheckedAgendaId: null,
      consecutiveEndCount: 0 // 连续检测到议程结束的次数
    }
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.checkIfNeedsScroll);
    if (this.agendaTimer) {
      clearTimeout(this.agendaTimer);
    }
  },
  async mounted() {
    this.checkIfNeedsScroll();
    window.addEventListener('resize', this.checkIfNeedsScroll);
    await this.loadAgendaData();
    this.checkCurrentAgenda();
  },
  methods: {
    checkIfNeedsScroll() {
      const container = this.$refs.container;
      const textElement = this.$refs.textElement;

      if (!container || !textElement) return;

      const containerWidth = container.offsetWidth;
      const textWidth = textElement.offsetWidth;

      if (textWidth > containerWidth) {
        textElement.classList.add('scroll-animation');
      } else {
        textElement.classList.remove('scroll-animation');
      }
    },
    async loadAgendaData() {
      try {
        console.log("开始加载议程数据...");
        const response = await getList(1, 20, {});
        
        if (response && response.data && response.data.success) {
          const transformedData = dataTransformers.agenda(response.data);
          this.agendaList = transformedData;
        } else {
          throw new Error("API响应格式不正确");
        }
      } catch (error) {
        console.error("加载议程数据失败:", error);
        this.agendaList = this.defaultAgendaData;
      }
    },

    checkCurrentAgenda() {
      if (this.agendaTimer) {
        clearTimeout(this.agendaTimer);
      }

      if (!this.agendaList || this.agendaList.length === 0) {
        this.currentDescription = "暂无议程数据";
        return;
      }

      const now = new Date();
      const currentTimeStr = now.toTimeString().slice(0, 8);
      const currentDateStr = now.toISOString().split('T')[0];

      console.log(`当前时间: ${currentDateStr} ${currentTimeStr}`);

      const todayAgendas = this.agendaList.filter(agenda => {
        return agenda.date === currentDateStr;
      });

      if (todayAgendas.length === 0) {
        this.currentDescription = "今日暂无议程";
        // 今天没有议程，设置1小时后再检查
        this.agendaTimer = setTimeout(() => this.checkCurrentAgenda(), 3600000);
        return;
      }

      const sortedAgendas = [...todayAgendas].sort((a, b) => {
        const timeA = this.extractStartTime(a.time);
        const timeB = this.extractStartTime(b.time);
        return timeA.localeCompare(timeB);
      });

      let currentAgenda = null;
      for (const agenda of sortedAgendas) {
        if (agenda.time && this.isTimeInRange(currentTimeStr, agenda.time)) {
          this.currentDescription = agenda.description;
          currentAgenda = agenda;
          // 重置连续结束计数
          this.consecutiveEndCount = 0;
          break;
        }
      }

      if (currentAgenda) {
        this.lastCheckedAgendaId = currentAgenda.id; // 假设议程有id字段
        this.setEndTimeTimer(currentAgenda);
        return;
      }

      // 没有当前议程时重置计数
      this.consecutiveEndCount = 0;
      const nextAgenda = this.findNextAgenda(sortedAgendas, currentTimeStr);
      if (nextAgenda) {
        this.currentDescription = "当前没有进行中的议程";
        this.setStartTimeTimer(nextAgenda);
      } else {
        this.currentDescription = "今日没有更多议程";
        // 今天没有更多议程，设置1小时后再检查
        this.agendaTimer = setTimeout(() => this.checkCurrentAgenda(), 3600000);
      }
    },

    findNextAgenda(agendas, currentTimeStr) {
      return agendas.find(agenda => {
        const startTime = this.extractStartTime(agenda.time);
        return startTime > currentTimeStr;
      });
    },

    setEndTimeTimer(agenda) {
      const now = new Date();
      const [, endStr] = agenda.time.split("-");
      const endTimeStr = this.formatTime(endStr);
      
      const [hours, minutes, seconds] = endTimeStr.split(':').map(Number);
      const endTime = new Date();
      endTime.setHours(hours, minutes, seconds, 0);
      
      const timeDiff = endTime - now;
      
      // 防无限循环处理
      if (timeDiff <= 0) {
        this.consecutiveEndCount++;
        console.log(`当前议程已结束（第${this.consecutiveEndCount}次检测），准备刷新`);
        
        // 如果连续3次检测到已结束，说明可能没有下一个议程，设置延迟
        if (this.consecutiveEndCount >= 3) {
          console.log("连续检测到议程结束，设置1分钟后再检查");
          this.agendaTimer = setTimeout(() => {
            this.consecutiveEndCount = 0; // 重置计数
            this.checkCurrentAgenda();
          }, 60000); // 1分钟后再检查
          return;
        }
        
        // 否则短延迟后再检查（避免立即循环）
        this.agendaTimer = setTimeout(() => {
          this.checkCurrentAgenda();
        }, 1000); // 1秒后再检查
        return;
      }
      
      console.log(`当前议程将在 ${Math.round(timeDiff / 1000)} 秒后结束，届时自动刷新`);
      
      this.agendaTimer = setTimeout(() => {
        console.log(`议程结束时间已到（${endTimeStr}），自动刷新`);
        this.checkCurrentAgenda();
      }, timeDiff);
    },

    setStartTimeTimer(agenda) {
      const now = new Date();
      const startTimeStr = this.extractStartTime(agenda.time);
      
      const [hours, minutes, seconds] = startTimeStr.split(':').map(Number);
      const startTime = new Date();
      startTime.setHours(hours, minutes, seconds, 0);
      
      const timeDiff = startTime - now;
      
      console.log(`下一个议程将在 ${Math.round(timeDiff / 1000)} 秒后开始，届时自动刷新`);
      
      this.agendaTimer = setTimeout(() => {
        console.log(`下一个议程开始时间已到（${startTimeStr}），自动刷新`);
        this.checkCurrentAgenda();
      }, timeDiff);
    },

    isTimeInRange(currentTime, timeRange) {
      const [startStr, endStr] = timeRange.split("-");
      const startTime = this.formatTime(startStr);
      const endTime = this.formatTime(endStr);
      return currentTime >= startTime && currentTime <= endTime;
    },

    formatTime(timeStr) {
      const trimmed = timeStr.trim();
      if (trimmed.length === 5) {
        return `${trimmed}:00`;
      }
      return trimmed;
    },

    extractStartTime(timeStr) {
      if (!timeStr) return "00:00:00";
      const startTime = timeStr.split("-")[0].trim();
      if (startTime.length === 5) {
        return startTime + ":00";
      }
      return startTime || "00:00:00";
    },
  },
};
</script>

<style scoped>
.content-column {
  width: 100%;
  padding: 4vw 6vw;
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-height: 18vh;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

@supports (backdrop-filter: blur(10px)) or (-webkit-backdrop-filter: blur(10px)) {
  .content-column {
    /* 现代浏览器：使用 backdrop-filter 实现最佳磨砂效果 */
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

.scroll-container {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
}

.scroll-text {
  color: #ffffff;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  margin: 0;
  font-size: 1.1rem;
  letter-spacing: 0.3px;
  display: inline-block;
  white-space: nowrap;
}

.scroll-text.scroll-animation {
  animation: scroll 50s linear infinite;
  padding-left: 100%;
}

@keyframes scroll {
  0% { transform: translateX(0); }
  100% { transform: translateX(-100%); }
}

@media (max-width: 320px) {
  .content-column {
    height: 25vh;
    min-height: 140px;
    padding: 3vw 4vw;
  }
  
  .scroll-text { font-size: 0.85rem; }
}

@media (min-width: 480px) {
  .scroll-text { font-size: 1rem; }
}
</style>
    