<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.userAgenda.mapper.UserAgendaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userAgendaResultMap" type="org.springblade.modules.hy.userAgenda.pojo.entity.UserAgendaEntity">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="agenda_id" property="agendaId"/>
        <result column="agenda_date" property="agendaDate"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="topic" property="topic"/>
        <result column="comment" property="comment"/>
        <result column="agenda_start_time" property="agendaStartTime"/>
        <result column="agenda_end_time" property="agendaEndTime"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectUserAgendaPage" resultMap="userAgendaResultMap">
        select * from hy_user_agenda where is_deleted = 0
    </select>


    <select id="exportUserAgenda" resultType="org.springblade.modules.hy.userAgenda.excel.UserAgendaExcel">
        SELECT * FROM hy_user_agenda ${ew.customSqlSegment}
    </select>

</mapper>
