<template>
  <div class="page-container">
    <!-- 页面内容 -->
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-bed"></i>
          <h2>住宿信息</h2>
          <p>会议期间的酒店住宿安排</p>
        </div>

        <!-- 住宿内容滚动容器 -->
        <div class="accommodation-scroll-container">
          <!-- 住宿状态 -->
          <div class="accommodation-status">
            <div class="status-card list-item">
              <div class="status-icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <div class="status-info">
                <h3>住宿已确认</h3>
                <p>您的住宿安排已确认，请查看详细信息</p>
              </div>
              <div class="status-badge">
                已预订
              </div>
            </div>
          </div>

          <!-- 酒店信息 -->
          <div class="hotel-info">
            <div class="hotel-card list-item">
              <div class="hotel-header">
                <div class="hotel-name">
                  <h3>{{ hotelData.name }}</h3>
                  <div class="hotel-rating">
                    <i v-for="n in hotelData.rating" :key="n" class="fas fa-star"></i>
                    <span>{{ hotelData.rating }}星级</span>
                  </div>
                </div>
                <div class="hotel-image">
                  <i class="fas fa-building"></i>
                </div>
              </div>

              <div class="hotel-details">
                <div v-for="detail in hotelData.details" :key="detail.title" class="detail-item">
                  <i :class="detail.icon"></i>
                  <div>
                    <strong>{{ detail.title }}</strong>
                    <p>{{ detail.content }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 房间信息 -->
          <div class="room-info">
            <h3 class="section-title">
              <i class="fas fa-bed"></i>
              房间详情
            </h3>
            <div class="room-card list-item">
              <div class="room-header">
                <div class="room-type">
                  <i class="fas fa-bed"></i>
                  <span>{{ roomData.type }}</span>
                </div>
                <div class="room-number">
                  房间号：<strong>{{ roomData.number }}</strong>
                </div>
              </div>

              <div class="room-details">
                <div v-for="feature in roomData.features" :key="feature.name" class="room-feature">
                  <i :class="feature.icon"></i>
                  <span>{{ feature.name }}</span>
                </div>
              </div>

              <div class="checkin-info">
                <div v-for="info in checkinData" :key="info.label" class="checkin-item">
                  <strong>{{ info.label }}</strong>
                  <span>{{ info.value }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 服务信息 -->
          <div class="hotel-services">
            <h3 class="section-title">
              <i class="fas fa-concierge-bell"></i>
              酒店服务
            </h3>
            <div class="services-grid list-item">
              <div v-for="service in hotelServices" :key="service.name" class="service-item">
                <i :class="service.icon"></i>
                <span>{{ service.name }}</span>
              </div>
            </div>
          </div>

          <!-- 交通指南 -->
          <div class="transportation">
            <h3 class="section-title">
              <i class="fas fa-location-arrow"></i>
              交通指南
            </h3>
            <div class="transport-list list-item">
              <div v-for="transport in transportData" :key="transport.title" class="transport-item">
                <div class="transport-icon">
                  <i :class="transport.icon"></i>
                </div>
                <div class="transport-info">
                  <strong>{{ transport.title }}</strong>
                  <p>{{ transport.description }}</p>
                  <small>{{ transport.time }}</small>
                </div>
              </div>
            </div>
          </div>

          <!-- 联系信息 -->
          <div class="contact-info">
            <h3 class="section-title">
              <i class="fas fa-phone-alt"></i>
              联系信息
            </h3>
            <div class="contact-list list-item">
              <div v-for="contact in contactData" :key="contact.type" class="contact-item">
                <div class="contact-icon">
                  <i :class="contact.icon"></i>
                </div>
                <div class="contact-details">
                  <strong>{{ contact.type }}</strong>
                  <p>{{ contact.value }}</p>
                </div>
                <button class="contact-btn submit-btn" @click="handleContact(contact)">
                  <i class="fas fa-phone"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- 温馨提示 -->
          <div class="tips-section">
            <h3 class="section-title">
              <i class="fas fa-lightbulb"></i>
              温馨提示
            </h3>
            <div class="tips-list list-item">
              <div v-for="tip in tips" :key="tip.title" class="tip-item">
                <i :class="tip.icon"></i>
                <div>
                  <strong>{{ tip.title }}</strong>
                  <p>{{ tip.content }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 底部导航栏占位符 -->
    <div class="bottom-nav-placeholder"></div>
  </div>
</template>

<script>
import { getList } from "@/api/hotel/hotel";
export default {
  name: 'MyAccommodation',
  data() {
    return {
      // 酒店数据
      hotelData: {
        name: '',
        rating: 0,
        details: []
      },
      // 房间数据
      roomData: {
        type: '',
        number: '',
        features: []
      },
      // 入住信息
      checkinData: [],
      // 酒店服务
      hotelServices: [],
      // 交通指南
      transportData: [],
      // 联系信息
      contactData: [],
      // 温馨提示
      tips: []
    };
  },
  methods: {
    // 处理联系操作
    handleContact(contact) {
      if (this.$message) {
        this.$message.info(`拨打电话：${contact.value}`);
      } else {
        alert(`拨打电话：${contact.value}`);
      }
    },
    async loadHotelData() {
      try {
        console.log("开始加载酒店数据...");
        const response = await getList(1, 20, {});
        
        if (response && response.data && response.data.code === 200) {
          // 提取酒店列表数据
          const hotelData = response.data.data;
          const hotelList = hotelData.records || [];
          
          console.log("酒店数据加载成功，共", hotelList.length, "条记录");
          console.log("酒店列表:", hotelList);
          
          // 检查是否有酒店数据
          if (hotelList.length > 0) {
            // 获取第0条酒店数据
            const firstHotel = hotelList[0];
            this.updateHotelData(firstHotel);
          }
          
          return hotelList;
        } else {
          throw new Error(`API响应错误: ${response?.data?.message || '未知错误'}`);
        }
      } catch (error) {
        console.error("加载酒店数据失败:", error.message || error);
        return []; // 出错时返回空数组，避免后续处理报错
      }
    },
    // 更新酒店数据到组件状态
    updateHotelData(hotel) {
      // 更新酒店基本信息
      this.hotelData = {
        name: hotel.hotelName || '',
        rating: hotel.hotelRating || 0,
        details: [
          {
            icon: 'fas fa-map-marker-alt',
            title: '酒店地址',
            content: hotel.hotelAddress || ''
          },
          {
            icon: 'fas fa-walking',
            title: '距离会场',
            content: hotel.distanceToVenue || ''
          },
          {
            icon: 'fas fa-phone',
            title: '联系电话',
            content: hotel.hotelPhone || ''
          }
        ]
      };
      
      // 更新房间信息 - 统一使用fas fa-bed图标
      this.roomData = {
        type: hotel.roomType || '',
        number: hotel.roomNumber || '未知',
        features: this.formatRoomFeatures(hotel.roomFeatures, [
          { name: '免费WiFi' },
          { name: '液晶电视' },
          { name: '空调' },
          { name: '独立卫浴' }
        ])
      };
      
      // 更新入住信息
      this.checkinData = [
        {
          label: '入住时间：',
          value: hotel.checkinTime ? this.formatDate(hotel.checkinTime) : ''
        },
        {
          label: '退房时间：',
          value: hotel.checkoutTime ? this.formatDate(hotel.checkoutTime) : ''
        },
        {
          label: '住宿天数：',
          value: hotel.stayDays ? `${hotel.stayDays}晚` : ''
        }
      ];
      
      // 更新酒店服务
      this.hotelServices = this.parseJsonData(hotel.hotelServices, [
        { icon: 'fas fa-concierge-bell', name: '24小时前台' },
        { icon: 'fas fa-utensils', name: '餐厅服务' },
        { icon: 'fas fa-dumbbell', name: '健身中心' }
      ]).map(service => ({
        ...service,
        icon: this.getServiceIcon(service.name)
      }));
      
      // 更新交通指南
      this.transportData = this.parseJsonData(hotel.transportInfo, [
        {
          icon: 'fas fa-plane',
          title: '从机场',
          description: '',
          time: ''
        }
      ]).map(transport => ({
        ...transport,
        icon: this.getTransportIcon(transport.title)
      }));
      
      // 更新联系信息
      this.contactData = this.parseJsonData(hotel.contactInfo, [
        {
          icon: 'fas fa-phone',
          type: '酒店前台',
          value: ''
        }
      ]).map(contact => ({
        ...contact,
        icon: this.getContactIcon(contact.type)
      }));
      
      // 更新温馨提示
      this.tips = this.parseJsonData(hotel.hotelTips, [
        {
          icon: 'fas fa-clock',
          title: '入住时间',
          content: ''
        }
      ]).map(tip => ({
        ...tip,
        icon: this.getTipIcon(tip.title)
      }));
    },
    // 解析JSON数据，处理可能的解析错误
    parseJsonData(jsonString, defaultValue) {
      if (!jsonString) return defaultValue;
      try {
        return JSON.parse(jsonString);
      } catch (e) {
        console.error('解析JSON失败:', e);
        return defaultValue;
      }
    },
    // 格式化房间特征
    formatRoomFeatures(jsonString, defaultFeatures) {
      // 解析JSON数据
      const parsedFeatures = this.parseJsonData(jsonString, []);
      
      // 如果解析后没有数据，使用默认特征
      if (parsedFeatures.length === 0) {
        return defaultFeatures.map(feature => ({
          ...feature,
          icon: 'fas fa-info'
        }));
      }
      
      // 为每个特征添加统一的床图标
      return parsedFeatures.map(feature => {
        // 如果是字符串格式
        if (typeof feature === 'string') {
          return {
            name: feature,
            icon: 'fas fa-info'
          };
        }
        
        // 如果是对象格式
        return {
          ...feature,
          icon: 'fas fa-info'
        };
      });
    },
    // 格式化日期显示
    formatDate(dateString) {
      if (!dateString) return '';
      // 简单处理日期格式，可根据需要完善
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
    },
    // 根据服务名称获取对应的图标
    getServiceIcon(serviceName) {
      const iconMap = {
        '24小时前台': 'fas fa-concierge-bell',
        '餐厅服务': 'fas fa-utensils',
        '健身中心': 'fas fa-dumbbell',
        '游泳池': 'fas fa-swimming-pool',
        '停车场': 'fas fa-car',
        '行李服务': 'fas fa-luggage-cart'
      };
      return iconMap[serviceName] || 'fas fa-check';
    },
    // 根据交通方式获取对应的图标
    getTransportIcon(title) {
      if (title.includes('机场')) return 'fas fa-plane';
      if (title.includes('火车')) return 'fas fa-train';
      if (title.includes('地铁')) return 'fas fa-subway';
      return 'fas fa-location-arrow';
    },
    // 根据联系类型获取对应的图标
    getContactIcon(type) {
      if (type.includes('酒店')) return 'fas fa-phone';
      if (type.includes('客服')) return 'fas fa-headset';
      if (type.includes('紧急')) return 'fas fa-exclamation-triangle';
      return 'fas fa-info-circle';
    },
    // 根据提示标题获取对应的图标
    getTipIcon(title) {
      if (title.includes('时间')) return 'fas fa-clock';
      if (title.includes('证件')) return 'fas fa-id-card';
      if (title.includes('网络')) return 'fas fa-wifi';
      if (title.includes('禁烟')) return 'fas fa-smoking-ban';
      return 'fas fa-lightbulb';
    }
  },
  created(){
    this.loadHotelData();
  }
};
</script>

<style scoped>
/* 页面容器 */
.page-container {
  width: 100%;
  max-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 页面内容 */
.page-content {
  margin-top: 15px;
}

/* 容器样式 - 统一磨砂玻璃效果 */
.list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 15px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
  0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
  max-height: 85vh;
}

/* 住宿内容滚动容器 */
.accommodation-scroll-container {
  max-height: calc(85vh - 200px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: 5px;
  margin-bottom: 25px;
}

/* 滚动条美化 */
.accommodation-scroll-container::-webkit-scrollbar {
  width: 5px;
}

.accommodation-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.accommodation-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
}

.accommodation-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
}

/* 标题区域样式统一 */
.form-header {
  text-align: center;
  margin-bottom: 25px;
  color: #ffffff;
}

.form-header i {
  font-size: 36px;
  color: #07D3F0;
  margin-bottom: 10px;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.form-header h2 {
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 10px;
}

/* 区域标题样式 */
.section-title {
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  color: white;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-size: 17px;
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid rgba(7, 211, 240, 0.2);
  backdrop-filter: blur(8px);
}

/* 列表项样式 - 卡片风格 */
.list-item {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
  position: relative;
  overflow: hidden;
}

/* 卡片悬停效果 */
.list-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.list-item:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.list-item:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

/* 住宿状态 */
.accommodation-status {
  margin: 20px 0;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-icon i {
  font-size: 24px;
  color: #07D3F0;
}

.status-info {
  flex: 1;
}

.status-info h3 {
  margin: 0 0 5px 0;
  color: #ffffff;
  font-size: 16px;
}

.status-info p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.status-badge {
  background: rgba(7, 211, 240, 0.2);
  color: #07D3F0;
  padding: 5px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 酒店信息 */
.hotel-info {
  margin: 20px 0;
}

.hotel-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.hotel-name h3 {
  margin: 0 0 8px 0;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
}

.hotel-rating {
  display: flex;
  align-items: center;
  gap: 3px;
}

.hotel-rating i {
  color: #ffc107;
  font-size: 12px;
}

.hotel-rating span {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin-left: 5px;
}

.hotel-image {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hotel-image i {
  font-size: 20px;
  color: #07D3F0;
}

.hotel-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.detail-item i {
  font-size: 14px;
  color: #07D3F0;
  margin-top: 2px;
  flex-shrink: 0;
}

.detail-item strong {
  display: block;
  color: #ffffff;
  margin-bottom: 3px;
  font-weight: 500;
  font-size: 14px;
}

.detail-item p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

/* 房间信息 */
.room-info {
  margin: 20px 0;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.room-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.room-type i {
  color: #07D3F0;
  font-size: 16px;
}

.room-type span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.room-number {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.room-number strong {
  color: #ffffff;
}

.room-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.room-feature {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.room-feature i {
  font-size: 14px;
  color: #07D3F0;
}

.room-feature span {
  font-size: 12px;
  color: #ffffff;
}

.checkin-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkin-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.checkin-item:last-child {
  border-bottom: none;
}

.checkin-item strong {
  color: #ffffff;
  font-size: 12px;
}

.checkin-item span {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

/* 酒店服务 */
.hotel-services {
  margin: 20px 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  padding: 20px;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 15px 10px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
}

.service-item i {
  font-size: 20px;
  color: #07D3F0;
  margin-bottom: 8px;
}

.service-item span {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
}

/* 交通指南 */
.transportation {
  margin: 20px 0;
}

.transport-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 20px;
}

.transport-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.transport-icon {
  width: 40px;
  height: 40px;
  background: rgba(7, 211, 240, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.transport-icon i {
  color: #07D3F0;
  font-size: 16px;
}

.transport-info strong {
  display: block;
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 5px;
}

.transport-info p {
  margin: 0 0 3px 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.transport-info small {
  color: #07D3F0;
  font-size: 11px;
  font-weight: 500;
}

/* 联系信息 */
.contact-info {
  margin: 20px 0;
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 20px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.contact-icon {
  width: 35px;
  height: 35px;
  background: rgba(7, 211, 240, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-icon i {
  font-size: 14px;
  color: #07D3F0;
}

.contact-details {
  flex: 1;
}

.contact-details strong {
  display: block;
  color: #ffffff;
  margin-bottom: 3px;
  font-weight: 500;
  font-size: 14px;
}

.contact-details p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

/* 温馨提示 */
.tips-section {
  margin: 20px 0;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 20px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.tip-item i {
  font-size: 16px;
  color: #07D3F0;
  margin-top: 2px;
  flex-shrink: 0;
}

.tip-item strong {
  display: block;
  color: #ffffff;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 14px;
}

.tip-item p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1.4;
}

/* 按钮样式统一 */
.submit-btn {
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  color: white;
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.submit-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.submit-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(7, 211, 240, 0.2);
}

/* 底部导航栏占位符 */
.bottom-nav-placeholder {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 10vh;
  z-index: 10;
  pointer-events: none;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .list-container {
    padding: 15px;
  }

  .form-header i {
    font-size: 40px;
  }

  .form-header h2 {
    font-size: 20px;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .room-details {
    grid-template-columns: 1fr;
  }

  .accommodation-scroll-container {
    max-height: calc(85vh - 200px);
  }
}
</style>
    