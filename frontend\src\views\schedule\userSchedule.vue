<template>
  <basic-container>
    <avue-crud :option="option"
               v-model:search="search"
               v-model:page="page"
               v-model="form"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.userSchedule_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="info"
                   plain
                   icon="el-icon-document"
                   @click="handleDownloadTemplate">下载模板
        </el-button>
        <el-button type="primary"
                   plain
                   icon="el-icon-upload"
                   @click="handleImport">导 入
        </el-button>
        <el-button type="warning"
                   plain
                   icon="el-icon-download"
                   @click="handleExport">导 出
        </el-button>
        
      </template>

      <!-- 自定义操作栏 -->
      <template #menu="{ row }">
        <el-button
          type="primary"
          text
          @click="handleDiningManage(row)">
          <el-icon><Dish /></el-icon>
          用餐管理
        </el-button>
        <el-button
          type="primary"
          text
          @click="handleAgendaManage(row)">
          <el-icon><Calendar /></el-icon>
          会议管理
        </el-button>
      </template>

    </avue-crud>

    <!-- 用餐管理弹窗 -->
    <el-dialog
      v-model="diningDialogVisible"
      :title="`${currentUserName} - 用餐管理`"
      width="80%"
      :before-close="handleDiningDialogClose">
      <avue-crud
        :option="diningOption"
        :search.sync="diningSearch"
        :page.sync="diningPage"
        v-model="diningForm"
        :table-loading="diningLoading"
        :data="diningData"
        ref="diningCrud"
        @row-update="diningRowUpdate"
        @row-save="diningRowSave"
        @row-del="diningRowDel"
        @refresh-change="diningRefreshChange"
        @on-load="diningOnLoad">
      </avue-crud>
    </el-dialog>

    <!-- 用户会议管理弹窗 -->
    <el-dialog
      v-model="agendaDialogVisible"
      :title="`${currentUserName} - 会议管理`"
      width="80%"
      :before-close="handleAgendaDialogClose">
      <avue-crud
        :option="agendaOption"
        :search.sync="agendaSearch"
        :page.sync="agendaPage"
        v-model="agendaForm"
        :table-loading="agendaLoading"
        :data="agendaData"
        ref="agendaCrud"
        @row-update="agendaRowUpdate"
        @row-save="agendaRowSave"
        @row-del="agendaRowDel"
        @refresh-change="agendaRefreshChange"
        @on-load="agendaOnLoad"
        @change="handleAgendaChange">
      </avue-crud>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, importUserSchedule, downloadTemplate} from "@/api/schedule/userSchedule";
  import option from "@/option/schedule/userSchedule";
  import {mapGetters} from "vuex";
  import {exportBlob} from "@/api/common";
  import {getToken} from '@/utils/auth';
  import {downloadXls} from "@/utils/util";
  import {dateNow} from "@/utils/date";
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';
  // 用餐管理相关导入
  import {getList as getDiningList, getDetail as getDiningDetail, add as addDining, update as updateDining, remove as removeDining} from '@/api/dinner/dinner';
  import diningOption from '@/option/dinner/dinner';
  // 用户会议管理相关导入
  import {getList as getAgendaList, getDetail as getAgendaDetail, add as addAgenda, update as updateAgenda, remove as removeAgenda} from '@/api/userAgenda/userAgenda';
  import agendaOption from '@/option/userAgenda/userAgenda';

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        // 导入相关数据
        importLoading: false,
        // 用餐管理相关数据
        diningDialogVisible: false,
        diningLoading: false,
        diningForm: {},
        diningSearch: {},
        diningPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0,
        },
        diningData: [],
        diningOption: diningOption,
        currentUserId: null,
        currentUserName: '',
        // 用户会议管理相关数据
        agendaDialogVisible: false,
        agendaLoading: false,
        agendaForm: {},
        agendaSearch: {},
        agendaPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0,
        },
        agendaData: [],
        agendaOption: agendaOption,
        // 记录上一次选择的议程ID，用于检测议程是否真正发生变化
        lastSelectedAgendaId: null,
      };
    },
    mounted() {
      // 动态设置用户选择数据
      this.loadUserOptions();
      // 动态设置酒店选择数据
      this.loadHotelOptions();
      // 动态设置议程选择数据
      this.loadAgendaOptions();
    },

    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.userSchedule_add, false),
          viewBtn: this.validData(this.permission.userSchedule_view, false),
          delBtn: this.validData(this.permission.userSchedule_delete, false),
          editBtn: this.validData(this.permission.userSchedule_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // Excel导入
      handleImport() {
        // 创建文件输入元素
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = (event) => {
          const file = event.target.files[0];
          if (file) {
            this.uploadFile(file);
          }
        };
        input.click();
      },
      // 上传文件
      uploadFile(file) {
        this.importLoading = true;
        importUserSchedule(file).then(res => {
          this.importLoading = false;
          // 获取实际的导入结果数据
          const importResult = res.data.data;

          if (importResult.success) {
            this.$message.success(importResult.message);
            this.onLoad(this.page); // 刷新列表
          } else {
            // 显示导入结果统计信息
            let resultMsg = `导入完成！总计${importResult.totalCount}条，成功${importResult.successCount}条，失败${importResult.failCount}条`;
            if (importResult.successCount > 0) {
              resultMsg += `（新增${importResult.insertCount}条，更新${importResult.updateCount}条）`;
            }

            this.$message.warning(resultMsg);

            // 显示详细错误信息
            if (importResult.errorMessages && importResult.errorMessages.length > 0) {
              const errorMsg = importResult.errorMessages.slice(0, 10).join('\n'); // 显示前10个错误
              this.$alert(errorMsg, '导入错误详情', {
                confirmButtonText: '确定',
                type: 'warning',
                customClass: 'import-error-dialog'
              });
            }

            // 如果有成功的记录，刷新列表
            if (importResult.successCount > 0) {
              this.onLoad(this.page);
            }
          }
        }).catch(error => {
          this.importLoading = false;
          this.$message.error('导入失败：' + error.message);
        });
      },
      // 下载模板
      handleDownloadTemplate() {
        downloadTemplate().then(res => {
          const blob = new Blob([res.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `用户日程信息导入模板_${dateNow()}.xlsx`;
          link.click();
          window.URL.revokeObjectURL(url);
          this.$message.success('模板下载成功');
        }).catch(error => {
          this.$message.error('模板下载失败：' + error.message);
        });
      },
      loadUserOptions() {
        // 动态加载用户选择数据
        import('@/api/system/user').then(userApi => {
          userApi.getUserList().then(res => {
            if (res.data && res.data.data) {
              const userColumn = this.option.column.find(col => col.prop === 'userId');
              if (userColumn) {
                // 移除 dicUrl，使用 dicData
                delete userColumn.dicUrl;
                userColumn.dicData = res.data.data;
              }
            }
          }).catch(error => {
            console.error('加载用户列表失败:', error);
          });
        });
      },
      loadHotelOptions() {
        // 动态加载酒店选择数据
        import('@/api/hotel/hotel').then(hotelApi => {
          hotelApi.getList(1, 1000, {}).then(res => {
            if (res.data && res.data.data && res.data.data.records) {
              const hotelColumn = this.option.column.find(col => col.prop === 'hotelId');
              if (hotelColumn) {
                // 移除 dicUrl，使用 dicData
                delete hotelColumn.dicUrl;
                hotelColumn.dicData = res.data.data.records;
                // 强制更新组件
                this.$forceUpdate();
              }
            }
          }).catch(error => {
            console.error('加载酒店列表失败:', error);
          });
        });
      },

      loadAgendaOptions() {
        // 动态加载议程选择数据
        import('@/api/agenda/agenda').then(agendaApi => {
          agendaApi.getList(1, 1000, {}).then(res => {
            if (res.data && res.data.data && res.data.data.records) {
              const agendaColumn = this.agendaOption.column.find(col => col.prop === 'agendaId');
              if (agendaColumn) {
                // 移除 dicUrl，使用 dicData
                delete agendaColumn.dicUrl;
                agendaColumn.dicData = res.data.data.records;
                // 强制更新组件
                this.$forceUpdate();
              }
            }
          }).catch(error => {
            console.error('加载议程列表失败:', error);
          });
        });
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },

      handleExport() {
        let downloadUrl = `/hy/user-schedule/export-user-schedule?${this.website.tokenHeader}=${getToken()}`;
        const {
            userId,
            userRealName
        } = this.query;
        let values = {
            userId_equal: userId,
            userRealName_like: userRealName,
        };
        this.$confirm("是否导出数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          NProgress.start();
          exportBlob(downloadUrl, values).then(res => {
            downloadXls(res.data, `用户日程表${dateNow()}.xlsx`);
            NProgress.done();
          })
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          // 使用分页查询获取完整的用户日程信息，包括关联的用户信息和酒店信息
          getList(1, 1, { id_equal: this.form.id }).then(res => {
            if (res.data && res.data.data && res.data.data.records && res.data.data.records.length > 0) {
              this.form = res.data.data.records[0];
              // 处理酒店ID为-1的情况
              if (this.form.hotelId == -1) {
                this.form.hotelId = null;
              }
            } else {
              // 如果分页查询没有结果，使用详情查询
              getDetail(this.form.id).then(res => {
                this.form = res.data.data;
                // 处理酒店ID为-1的情况
                if (this.form.hotelId == -1) {
                  this.form.hotelId = null;
                }
              });
            }
          }).catch(() => {
            // 如果分页查询失败，使用详情查询
            getDetail(this.form.id).then(res => {
              this.form = res.data.data;
              // 处理酒店ID为-1的情况
              if (this.form.hotelId == -1) {
                this.form.hotelId = null;
              }
            });
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page) {
        this.loading = true;

        const {
          userId,
          userRealName
        } = this.query;

        let values = {
          userId_equal: userId,
          userRealName: userRealName,
        };

        getList(page.currentPage, page.pageSize, values).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },

      // 用餐管理相关方法
      handleDiningManage(row) {
        this.currentUserId = row.userId;
        this.currentUserName = row.userName || row.userRealName || '用户';
        this.diningDialogVisible = true;
        this.diningOnLoad(this.diningPage);
      },

      handleDiningDialogClose() {
        this.diningDialogVisible = false;
        this.currentUserId = null;
        this.currentUserName = '';
        this.diningData = [];
      },

      diningOnLoad(page) {
        this.diningLoading = true;
        getDiningList(page.currentPage, page.pageSize, {
          ...this.diningSearch,
          userId_equal: parseInt(this.currentUserId)
        }).then(res => {
          const data = res.data.data;
          this.diningPage.total = data.total;
          this.diningData = data.records;
          this.diningLoading = false;
        }).catch(() => {
          this.diningLoading = false;
        });
      },

      diningRefreshChange() {
        this.diningOnLoad(this.diningPage);
      },

      diningRowSave(row, done, loading) {
        row.userId = parseInt(this.currentUserId);
        // 处理日期格式
        if (row.dinnerDate && typeof row.dinnerDate === 'string') {
          // 如果是字符串格式的日期，转换为标准格式
          row.dinnerDate = row.dinnerDate + ' 00:00:00';
        }
        addDining(row).then(() => {
          this.diningOnLoad(this.diningPage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }).catch(() => {
          loading();
        });
      },

      diningRowUpdate(row, index, done, loading) {
        // 处理日期格式
        if (row.dinnerDate && typeof row.dinnerDate === 'string') {
          // 如果是字符串格式的日期，转换为标准格式
          row.dinnerDate = row.dinnerDate + ' 00:00:00';
        }
        updateDining(row).then(() => {
          this.diningOnLoad(this.diningPage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }).catch(() => {
          loading();
        });
      },

      diningRowDel(row) {
        this.$confirm('确定将选择数据删除?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          return removeDining(row.id);
        }).then(() => {
          this.diningOnLoad(this.diningPage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
      },

      // 用户会议管理相关方法
      handleAgendaManage(row) {
        this.currentUserId = row.userId;
        this.currentUserName = row.userName || row.userRealName || '用户';
        this.agendaDialogVisible = true;
        // 重置议程选择状态
        this.lastSelectedAgendaId = null;
        // 确保议程数据已加载
        this.loadAgendaOptions();
        this.agendaOnLoad(this.agendaPage);
      },

      handleAgendaDialogClose() {
        this.agendaDialogVisible = false;
        this.currentUserId = null;
        this.currentUserName = '';
        this.agendaData = [];
        // 重置议程选择状态
        this.lastSelectedAgendaId = null;
      },

      agendaOnLoad(page) {
        this.agendaLoading = true;
        getAgendaList(page.currentPage, page.pageSize, {
          ...this.agendaSearch,
          userId_equal: parseInt(this.currentUserId)
        }).then(res => {
          const data = res.data.data;
          this.agendaPage.total = data.total;
          this.agendaData = data.records;
          this.agendaLoading = false;
        }).catch(() => {
          this.agendaLoading = false;
        });
      },

      agendaRefreshChange() {
        this.agendaOnLoad(this.agendaPage);
      },

      agendaRowSave(row, done, loading) {
        row.userId = parseInt(this.currentUserId);
        // 处理日期格式
        if (row.agendaDate && typeof row.agendaDate === 'string') {
          row.agendaDate = row.agendaDate + ' 00:00:00';
        }
        addAgenda(row).then(() => {
          this.agendaOnLoad(this.agendaPage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }).catch(() => {
          loading();
        });
      },

      agendaRowUpdate(row, index, done, loading) {
        // 处理日期格式
        if (row.agendaDate && typeof row.agendaDate === 'string') {
          row.agendaDate = row.agendaDate + ' 00:00:00';
        }
        updateAgenda(row).then(() => {
          this.agendaOnLoad(this.agendaPage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }).catch(() => {
          loading();
        });
      },

      agendaRowDel(row) {
        this.$confirm('确定将选择数据删除?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          return removeAgenda(row.id);
        }).then(() => {
          this.agendaOnLoad(this.agendaPage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
      },

      handleAgendaChange(formData) {
        // 当选择议程时，自动填充相关信息
        // console.log('handleAgendaChange 参数:', formData);

        try {
          // 检查是否有agendaId值
          if (!formData || !formData.agendaId) {
            // 如果没有议程ID，重置记录的上次选择
            this.lastSelectedAgendaId = null;
            return;
          }

          const agendaId = formData.agendaId;

          // 检查议程是否真正发生了变化
          if (this.lastSelectedAgendaId === agendaId) {
            // 议程没有变化，不执行填充操作
            return;
          }

          // 更新记录的议程ID
          this.lastSelectedAgendaId = agendaId;

          // 从已加载的议程数据中查找选中的议程
          const agendaColumn = this.agendaOption.column.find(col => col.prop === 'agendaId');
          if (!agendaColumn || !agendaColumn.dicData) {
            return;
          }

          const selectedAgenda = agendaColumn.dicData.find(agenda => agenda.id == agendaId);

          if (selectedAgenda) {
            // 自动填充议程相关信息
            this.$nextTick(() => {
              // 格式化日期和时间
              const agendaDate = selectedAgenda.date || ''; // 只使用日期部分，如 "2025-09-15"
              const startTime = selectedAgenda.date && selectedAgenda.startTime
                ? `${selectedAgenda.date} ${selectedAgenda.startTime}`
                : '';
              const endTime = selectedAgenda.date && selectedAgenda.endTime
                ? `${selectedAgenda.date} ${selectedAgenda.endTime}`
                : '';

              const fillData = {
                agendaDate: agendaDate, // 日期格式：YYYY-MM-DD
                startTime: startTime,   // 日期时间格式：YYYY-MM-DD HH:mm:ss
                endTime: endTime,       // 日期时间格式：YYYY-MM-DD HH:mm:ss
                topic: selectedAgenda.topic || '',
                agendaStartTime: selectedAgenda.startTime || '', // 议程开始时间：HH:mm:ss
                agendaEndTime: selectedAgenda.endTime || '',     // 议程结束时间：HH:mm:ss
              };

              // 不自动填充备注信息，让用户自由编辑
              // comment 字段不会被自动填充

     

              // 直接更新表单数据对象
              Object.assign(formData, fillData);

              // 强制更新组件
              this.$forceUpdate();
            });
          }
        } catch (error) {
          console.error('handleAgendaChange 错误:', error);
        }
      }
    }
  };
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

/* 已移除text-ellipsis样式，不再需要 */
</style>

<style>
/* 导入错误对话框样式 */
.import-error-dialog .el-message-box__message {
  white-space: pre-line;
  max-height: 300px;
  overflow-y: auto;
  text-align: left;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.5;
}
</style>
