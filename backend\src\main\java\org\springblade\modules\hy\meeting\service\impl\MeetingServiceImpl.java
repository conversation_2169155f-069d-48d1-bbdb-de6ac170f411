/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.meeting.service.impl;

import org.springblade.modules.hy.meeting.pojo.entity.MeetingEntity;
import org.springblade.modules.hy.meeting.pojo.vo.MeetingVO;
import org.springblade.modules.hy.meeting.excel.MeetingExcel;
import org.springblade.modules.hy.meeting.mapper.MeetingMapper;
import org.springblade.modules.hy.meeting.service.IMeetingService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 会议表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class MeetingServiceImpl extends BaseServiceImpl<MeetingMapper, MeetingEntity> implements IMeetingService {

	public IPage<MeetingVO> selectMeetingPage(IPage<MeetingVO> page, MeetingVO meeting) {
		return page.setRecords(baseMapper.selectMeetingPage(page, meeting));
	}


	@Override
	public List<MeetingExcel> exportMeeting(Wrapper<MeetingEntity> queryWrapper) {
		List<MeetingExcel> meetingList = baseMapper.exportMeeting(queryWrapper);
		//meetingList.forEach(meeting -> {
		//	meeting.setTypeName(DictCache.getValue(DictEnum.YES_NO, Meeting.getType()));
		//});
		return meetingList;
	}

}
