<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.userschedule.mapper.UserScheduleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userScheduleResultMap" type="org.springblade.modules.hy.userschedule.pojo.entity.UserScheduleEntity">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="schedule_content" property="scheduleContent"/>
        <result column="dining_info" property="diningInfo"/>
        <result column="accommodation_info" property="accommodationInfo"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, user_id, schedule_content, dining_info, accommodation_info, 
        create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>

    <!-- 自定义分页查询 -->
    <select id="selectUserSchedulePage" resultType="org.springblade.modules.hy.userschedule.pojo.vo.UserScheduleVO">
        SELECT
            us.id,
            us.user_id,
            us.hotel_id,
            us.room_number,
            us.meeting_seat_number,
            us.create_user,
            us.create_dept,
            us.create_time,
            us.update_user,
            us.update_time,
            us.status,
            us.is_deleted,
            u.name AS user_name,
            u.real_name AS user_real_name,
            u.email AS user_email,
            u.phone AS user_phone,
            u.employee_number AS user_employee_number,
            u.room_number AS user_room_number,
            u.meeting_seat_number AS user_meeting_seat_number,
            h.hotel_name AS hotel_name,
            u.id_number AS id_number,
            u.work_unit AS work_unit,
            u.position AS position,
            CASE WHEN u.sex = 1 THEN '男' ELSE '女' END AS sex
        FROM hy_user_schedule us
        LEFT JOIN blade_user u ON us.user_id = u.id
        LEFT JOIN hy_hotel h ON us.hotel_id = h.id
        WHERE us.is_deleted = 0
        <if test="userSchedule.userId != null">
            AND us.user_id = #{userSchedule.userId}
        </if>
        <if test="userSchedule.userRealName != null and userSchedule.userRealName != ''">
            AND u.real_name LIKE CONCAT('%', #{userSchedule.userRealName}, '%')
        </if>
        <if test="userSchedule.status != null">
            AND us.status = #{userSchedule.status}
        </if>
        ORDER BY us.create_time DESC
    </select>

    <!-- 根据用户ID查询 -->
    <select id="selectByUserId" resultType="org.springblade.modules.hy.userschedule.pojo.vo.UserScheduleVO">
        SELECT
            us.id,
            us.user_id,
            us.hotel_id,
            us.room_number,
            us.meeting_seat_number,
            us.create_user,
            us.create_dept,
            us.create_time,
            us.update_user,
            us.update_time,
            us.status,
            us.is_deleted,
            u.name AS user_name,
            u.real_name AS user_real_name,
            u.email AS user_email,
            u.phone AS user_phone,
            u.employee_number AS user_employee_number,
            u.room_number AS user_room_number,
            u.meeting_seat_number AS user_meeting_seat_number,
            h.hotel_name AS hotel_name
        FROM hy_user_schedule us
        LEFT JOIN blade_user u ON us.user_id = u.id
        LEFT JOIN hy_hotel h ON us.hotel_id = h.id
        WHERE us.user_id = #{userId} AND us.is_deleted = 0
        LIMIT 1
    </select>

    <!-- 导出数据查询 -->
    <select id="exportUserSchedule" resultType="org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleExcel">
        SELECT
            us.user_id,
            u.name AS user_name,
            u.real_name AS user_real_name,
            u.email AS user_email,
            u.phone AS user_phone,
            u.employee_number AS user_employee_number,
            u.room_number AS user_room_number,
            u.meeting_seat_number AS user_meeting_seat_number
        FROM hy_user_schedule us
        LEFT JOIN blade_user u ON us.user_id = u.id
        WHERE us.is_deleted = 0
        <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
            AND ${ew.sqlSegment}
        </if>
    </select>

</mapper>
