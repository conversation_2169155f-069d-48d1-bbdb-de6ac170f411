/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.attendancerecord.mapper;

import org.springblade.modules.hy.attendancerecord.pojo.entity.AttendanceRecordEntity;
import org.springblade.modules.hy.attendancerecord.pojo.vo.AttendanceRecordVO;
import org.springblade.modules.hy.attendancerecord.excel.AttendanceRecordExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 参会签到记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
public interface AttendanceRecordMapper extends BaseMapper<AttendanceRecordEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param attendanceRecord 查询参数
	 * @return List<AttendanceRecordVO>
	 */
	List<AttendanceRecordVO> selectAttendanceRecordPage(IPage page, AttendanceRecordVO attendanceRecord);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<AttendanceRecordExcel>
	 */
	List<AttendanceRecordExcel> exportAttendanceRecord(@Param("ew") Wrapper<AttendanceRecordEntity> queryWrapper);

	/**
	 * 根据用户ID查询今日签到记录
	 *
	 * @param userId 用户ID
	 * @return 签到记录
	 */
	AttendanceRecordEntity selectTodayRecordByUserId(@Param("userId") Long userId);

	/**
	 * 根据用户ID和议程ID查询签到记录
	 *
	 * @param userId    用户ID
	 * @param agendaId  议程ID
	 * @return 签到记录
	 */
	AttendanceRecordEntity selectByUserIdAndAgendaId(@Param("userId") Long userId, @Param("agendaId") Long agendaId);

}
