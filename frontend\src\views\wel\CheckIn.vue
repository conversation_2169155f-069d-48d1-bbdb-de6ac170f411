<template>
  <!-- 签到内容 -->
  <div class="check-in-content">
    <!-- 已签到状态 -->
    <div class="check-in-status list-item" v-if="isCheckedIn">
      <div class="status-icon success">
        <i class="fas fa-check-circle"></i>
      </div>
      <h3>签到成功</h3>
      <p>您已于 {{ checkInTime }} 完成签到</p>
    </div>

    <!-- 未签到状态 -->
    <div class="check-in-card list-item" v-else>
      <div class="check-in-icon">
        <i class="fas fa-qrcode"></i>
      </div>
      <h2>扫码签到</h2>
      <p class="check-in-description">请扫描会议现场的二维码完成签到</p>

      <!-- 扫码按钮 -->
      <div class="scan-button-container">
        <button class="scan-btn" @click="openDingScan">
          <i class="fas fa-qrcode"></i>
          <span>扫码签到</span>
        </button>
        <p class="scan-tips">点击上方按钮扫描会议现场的签到二维码</p>
      </div>
    </div>
  </div>
</template>

<script>
import * as dd from 'dingtalk-jsapi'
import { mobileRequest, checkMobileAuth } from '@/utils/mobileRequest'

export default {
  name: 'CheckIn',
  data() {
    return {
      isCheckedIn: false,
      checkInTime: '',
      agendaId: null
    }
  },
  mounted() {
    // 检查用户登录状态
    if (!checkMobileAuth()) {
      this.$message && this.$message.error
        ? this.$message.error('请先登录')
        : alert('请先登录');
      return;
    }

    // 从URL参数获取议程ID
    this.agendaId = this.$route.query.agendaId ? parseInt(this.$route.query.agendaId) : null;

    if (this.agendaId) {
      this.checkCheckinStatus();
    }
  },
  methods: {
    goBack() {
      // 返回上一页
      this.$parent.goBack();
    },

    // 检查签到状态
    async checkCheckinStatus() {
      if (!this.agendaId) return;

      try {
        const response = await mobileRequest({
          method: 'get',
          url: `/api/attendance/status?agendaId=${this.agendaId}`
        });
        if (response.data.code === 200) {
          const data = response.data.data;
          this.isCheckedIn = data.isCheckedIn;
          if (data.isCheckedIn) {
            this.checkInTime = new Date(data.checkinTime).toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit'
            });
          }
        }
      } catch (error) {
        console.error('检查签到状态失败:', error);
        this.$message && this.$message.error
          ? this.$message.error('检查签到状态失败')
          : alert('检查签到状态失败');
      }
    },

    // 执行签到
    async performCheckin(agendaId) {
      try {
        const response = await mobileRequest({
          method: 'post',
          url: `/api/attendance/checkin/${agendaId}`
        });

        console.log('签到响应:', response.data); // 调试日志

        if (response.data.code === 200) {
          const data = response.data.data;
          // 签到成功
          this.isCheckedIn = true;
          this.checkInTime = new Date(data.checkinTime).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          });
          this.$message && this.$message.success
            ? this.$message.success('签到成功！')
            : alert('签到成功！');
        } else {
          // 签到失败，显示后端返回的错误信息（code=400等）
          const errorMsg = response.data.msg || response.data.message || '签到失败';
          this.$message && this.$message.error
            ? this.$message.error(errorMsg)
            : alert(errorMsg);
        }
      } catch (error) {
        console.error('签到请求异常:', error);
        // if (!error.response) {
          // 网络错误（没有response对象）
        //   const errorMsg = error.message || '网络错误，请重试';
        //   this.$message && this.$message.error
        //     ? this.$message.error(errorMsg)
        //     : alert(errorMsg);
        // }
        // 如果有response对象，说明是业务错误，axios拦截器已经处理了，不需要再显示
      }
    },

    // 钉钉扫一扫
    async openDingScan() {
      try {
        dd.scan({
          type: 'qr',
          onSuccess: (res) => {
            const text = res?.text || '';
            console.log('扫码结果:', text);

            // 检查是否是会议签到的URL
            if (text && text.includes('/mobile/checkin?agendaId=')) {
              // 提取议程ID
              const urlParams = new URLSearchParams(text.split('?')[1]);
              const agendaId = parseInt(urlParams.get('agendaId'));

              if (agendaId) {
                // 直接进行签到
                this.performCheckin(agendaId);
              } else {
                this.$message && this.$message.error
                  ? this.$message.error('无效的签到二维码')
                  : alert('无效的签到二维码');
              }
            } else {
              this.$message && this.$message.error
                ? this.$message.error('请扫描会议签到二维码')
                : alert('请扫描会议签到二维码');
            }
          },
          onFail: () => {
            this.$message && this.$message.error
              ? this.$message.error('扫码失败')
              : alert('扫码失败');
          }
        });
      } catch (e) {
        console.log(e)
        this.$message && this.$message.error
          ? this.$message.error('扫码异常')
          : alert('扫码异常');
      }
    },
  }
}
</script>

<style scoped>
/* 页面通用样式 - 与其他页面统一 */
.page-container {
  width: 100%;
  min-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

.check-in-container {
  width: 100%;
  min-height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  margin-bottom: 20px;
}

.page-header h1 {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  flex: 1;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.back-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(7, 211, 240, 0.3);
  color: #07D3F0;
  font-size: 20px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(7, 211, 240, 0.2);
  transform: translateY(-2px);
}

.header-placeholder {
  width: 40px;
}

.check-in-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  margin-top: 50px;
  padding: 0 15px;
}

/* 统一卡片样式 */
.list-item {
  background: rgba(255, 255, 255, 0.06);
  /* backdrop-filter: blur(10px); */
  border-radius: 15px;
  padding: 30px 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

/* 磨砂背景 */
.list-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url(/img/bg08.jpg) no-repeat 0/cover fixed;
  filter: blur(20px); /* 只模糊背景 */
  z-index: 1; /* 背景层在底层 */
}

/* 内容层（保持清晰） */
.list-item > * {
  position: relative;
  z-index: 2; /* 内容层在背景层上方，不受模糊影响 */
}

.check-in-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  border: 1px solid rgba(7, 211, 240, 0.3);
}

.check-in-icon i {
  font-size: 40px;
  color: #07D3F0;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.check-in-card h2 {
  font-size: 22px;
  color: #ffffff;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.check-in-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.5;
}

.scan-button-container {
  text-align: center;
  padding: 40px 20px;
}

.scan-btn {
  background: linear-gradient(135deg, #07D3F0 0%, #0BB5D1 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 20px 40px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(7, 211, 240, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin: 0 auto 20px;
  min-width: 200px;
}

.scan-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(7, 211, 240, 0.4);
  background: linear-gradient(135deg, #0BB5D1 0%, #07D3F0 100%);
}

.scan-btn:active {
  transform: translateY(0);
  box-shadow: 0 6px 20px rgba(7, 211, 240, 0.3);
}

.scan-btn i {
  font-size: 24px;
}

.scan-btn span {
  font-size: 18px;
}



.scan-tips {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

/* 统一按钮样式 */
.submit-btn {
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  color: white;
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 30px;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 20px auto 0;
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.submit-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(7, 211, 240, 0.2);
}

.check-in-status h3 {
  font-size: 22px;
  color: #ffffff;
  margin-bottom: 10px;
}

.check-in-status p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.status-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 40px;
}

.status-icon.success {
  background: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
  border: 1px solid rgba(46, 204, 113, 0.3);
}

/* 动画效果统一 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}
</style>
