<template>
  <div
    class="glowing-card"
    :style="{ '--glow': color || '#7b00ff' }"
    @click="$emit('navigate')"
  >
    <div class="card-content">
      <div class="card-icon">
        <i :class="iconClass"></i>
      </div>
      <div class="card-title">{{ title }}</div>
      <div class="card-subtitle">{{ subtitle }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GlowingCard',
  props: {
    iconClass: String,
    title: String,
    subtitle: String,
    color: String
  }
}
</script>

<style scoped>
.glowing-card {
  width: 100%;

  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #8575ed;
  box-shadow: 0 0 10px #432be2;

  border-radius: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-content {
  text-align: center;
  color: #2c2c2c;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 图标大小使用vw单位实现自适应 */
.card-icon i {
  font-size: 7vw;
  margin-bottom: 0.5vw;
  color: #432be2;
}

@supports (backdrop-filter: blur(10px)) or (-webkit-backdrop-filter: blur(10px)) {
  .glowing-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glow);
    box-shadow: 0 0 10px var(--glow);
  }  
  .card-content {
    color: #ffffff;
  }
  .card-icon i {
    color: var(--glow);
  }
}

/* 标题字体大小自适应 */
.card-title {
  font-size: 3.5vw;
  font-weight: bold;
  margin-bottom: 1vw;
}

/* 副标题字体大小自适应 */
.card-subtitle {
  font-size: 3vw;
  opacity: 0.8;
}

/* 针对不同屏幕尺寸的精细调整 */
@media (max-width: 380px) {
  .card-icon i {
    font-size: 6vw;
  }
  .card-title {
    font-size: 3.5vw;
    margin-bottom: 0;
  }
  .card-subtitle {
    font-size: 2.5vw;
  }
}

@media (min-width: 480px) {
  .card-icon i {
    font-size: 6vw;
  }
  .card-title {
    font-size: 4vw;
  }
  .card-subtitle {
    font-size: 2.8vw;
  }
}
</style>
