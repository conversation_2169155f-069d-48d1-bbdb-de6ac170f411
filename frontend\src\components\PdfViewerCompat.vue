<template>
  <div class="pdf-viewer-compat">
    <div class="pdf-toolbar">
      <div class="pdf-info">
        <span v-if="pageNum">{{ pageNum }} / {{ pageCount }}</span>
        <span v-else-if="loading">加载中...</span>
        <span v-else>未加载</span>
      </div>
      <div class="pdf-controls">
        <button @click="prevPage" :disabled="pageNum <= 1 || loading" class="pdf-btn">
          <i class="fas fa-chevron-left"></i>
        </button>
        <button @click="nextPage" :disabled="pageNum >= pageCount || loading" class="pdf-btn">
          <i class="fas fa-chevron-right"></i>
        </button>
        <button @click="zoomIn" :disabled="loading" class="pdf-btn">
          <i class="fas fa-search-plus"></i>
        </button>
        <button @click="zoomOut" :disabled="loading" class="pdf-btn">
          <i class="fas fa-search-minus"></i>
        </button>
<!--        <button @click="downloadPdf" :disabled="loading" class="pdf-btn">-->
<!--          <i class="fas fa-download"></i>-->
<!--        </button>-->
      </div>
    </div>

    <div class="pdf-container" ref="pdfContainer">
      <div v-if="loading" class="pdf-loading">
        <i class="fas fa-spinner fa-spin"></i>
        <span>PDF 加载中...</span>
      </div>
      <div v-if="error" class="pdf-error">
        <i class="fas fa-exclamation-triangle"></i>
        <h4>PDF预览不可用</h4>
        <p>{{ error }}</p>
        <div class="pdf-error-actions">
<!--          <button @click="downloadPdf" class="pdf-download-btn">-->
<!--            <i class="fas fa-download"></i> 下载查看-->
<!--          </button>-->
          <button @click="retryLoad" class="pdf-retry-btn">
            <i class="fas fa-redo"></i> 重试
          </button>
        </div>
        <div class="pdf-error-tip">
          <small>提示：如果预览失败，建议下载后查看PDF文档</small>
        </div>
      </div>
      <canvas ref="pdfCanvas" class="pdf-canvas" v-show="!loading && !error"></canvas>
    </div>
  </div>
</template>

<script>
// 兼容性版本 - 专门针对钉钉等环境优化，使用 PDF.js 3.x
export default {
  name: 'PdfViewerCompat',
  props: {
    pdfUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: '文档预览'
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      pageNum: 0,
      pageCount: 0,
      scale: 1.0
    };
  },
  // 使用非响应式属性存储 PDF 对象，避免 Vue 响应式系统破坏 PDF.js 私有成员
  beforeCreate() {
    this._pdfDocument = null;
    this._currentPage = null;
    this._renderTask = null;
    this._pdfjsLib = null;
  },
  async mounted() {
    await this.initPdfJs();
    if (this.pdfUrl && !this.error) {
      await this.$nextTick();
      await this.loadPdf();
    }
  },
  beforeUnmount() {
    this.cleanup();
  },
  watch: {
    pdfUrl: {
      async handler(newUrl, oldUrl) {
        if (newUrl !== oldUrl && newUrl) {
          await this.$nextTick();
          await this.loadPdf();
        }
      }
    }
  },
  methods: {
    // 初始化 PDF.js 3.x
    async initPdfJs() {
      try {
        console.log('正在初始化 PDF.js...');

        // 动态导入 PDF.js 3.x
        this._pdfjsLib = await import('pdfjs-dist');

        // 设置 worker - 3.x 版本使用 .js 文件
        this._pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
          'pdfjs-dist/build/pdf.worker.min.js',
          import.meta.url
        ).href;

        console.log('PDF.js 初始化成功，版本:', this._pdfjsLib.version);
        console.log('Worker URL:', this._pdfjsLib.GlobalWorkerOptions.workerSrc);

      } catch (err) {
        console.error('PDF.js 初始化失败:', err);
        this.error = 'PDF.js 库加载失败';
        this.loading = false;
      }
    },

    // 加载PDF文档
    async loadPdf() {
      if (!this._pdfjsLib) {
        this.error = 'PDF.js 未初始化';
        this.loading = false;
        return;
      }

      this.cleanup();
      this.loading = true;
      this.error = null;
      this.pageNum = 0;
      this.pageCount = 0;

      try {
        console.log('开始加载 PDF:', this.pdfUrl);

        // 使用 PDF.js 3.x 加载文档 - 更简单的配置，兼容性更好
        const loadingTask = this._pdfjsLib.getDocument({
          url: this.pdfUrl,
          httpHeaders: {},
          withCredentials: false
        });

        this._pdfDocument = await loadingTask.promise;
        this.pageCount = this._pdfDocument.numPages;

        console.log('PDF 加载成功，总页数:', this.pageCount);

        // 渲染第一页
        this.pageNum = 1;
        await this.renderPage(1);

        this.loading = false;
      } catch (err) {
        console.error('PDF加载失败:', err);
        this.error = err.message || '无法加载PDF文档';
        this.loading = false;
      }
    },

    // 渲染指定页面 - PDF.js 3.x
    async renderPage(num) {
      if (!this._pdfDocument) return;

      try {
        // 取消当前渲染任务
        if (this._renderTask) {
          try {
            this._renderTask.cancel();
          } catch (e) {
            // 忽略取消错误
          }
          this._renderTask = null;
        }

        // 获取页面
        this._currentPage = await this._pdfDocument.getPage(num);

        // 等待 DOM 更新
        await this.$nextTick();

        // 获取视口
        const viewport = this._currentPage.getViewport({ scale: this.scale });

        // 设置canvas
        const canvas = this.$refs.pdfCanvas;
        if (!canvas) {
          console.warn('Canvas 元素未找到，等待 DOM 渲染...');
          await new Promise(resolve => setTimeout(resolve, 100));
          await this.$nextTick();
          const retryCanvas = this.$refs.pdfCanvas;
          if (!retryCanvas) {
            throw new Error('Canvas 元素仍未找到');
          }
          // 使用重试获取的 canvas
          return this.renderToCanvas(retryCanvas, num);
        }

        const context = canvas.getContext('2d');

        // 设置canvas尺寸
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        // 清除画布
        context.clearRect(0, 0, canvas.width, canvas.height);

        // 渲染配置
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        };

        // 开始渲染
        this._renderTask = this._currentPage.render(renderContext);
        await this._renderTask.promise;
        this._renderTask = null;

        return this.renderToCanvas(canvas, num);
      } catch (err) {
        if (err && err.name !== 'RenderingCancelledException') {
          console.error('页面渲染失败:', err);
          this.error = `渲染页面 ${num} 失败: ${err.message}`;
        }
      }
    },

    // 实际渲染到 Canvas
    async renderToCanvas(canvas, num) {
      const context = canvas.getContext('2d');
      const viewport = this._currentPage.getViewport({ scale: this.scale });

      console.log('开始渲染到 Canvas，尺寸:', viewport.width, 'x', viewport.height);

      // 设置 canvas 尺寸
      canvas.width = viewport.width;
      canvas.height = viewport.height;

      // 清除画布
      context.clearRect(0, 0, canvas.width, canvas.height);

      // 渲染配置
      const renderContext = {
        canvasContext: context,
        viewport: viewport
      };

      this._renderTask = this._currentPage.render(renderContext);
      await this._renderTask.promise;
      this._renderTask = null;

      this.pageNum = num;
      console.log(`页面 ${num} 渲染成功`);
    },

    // 上一页
    prevPage() {
      if (this.pageNum <= 1) return;
      this.renderPage(this.pageNum - 1);
    },

    // 下一页
    nextPage() {
      if (this.pageNum >= this.pageCount) return;
      this.renderPage(this.pageNum + 1);
    },

    // 放大
    zoomIn() {
      this.scale = Math.min(this.scale + 0.2, 3.0);
      this.renderPage(this.pageNum);
    },

    // 缩小
    zoomOut() {
      this.scale = Math.max(this.scale - 0.2, 0.5);
      this.renderPage(this.pageNum);
    },

    // 下载PDF
    downloadPdf() {
      if (this.pdfUrl) {
        window.open(this.pdfUrl, '_blank');
      }
    },

    // 重试加载
    async retryLoad() {
      await this.loadPdf();
    },

    // 清理资源
    cleanup() {
      try {
        // 清理渲染任务
        if (this._renderTask) {
          this._renderTask.cancel();
          this._renderTask = null;
        }
      } catch (e) {
        console.warn('清理渲染任务失败:', e);
      }

      try {
        // 清理当前页面
        if (this._currentPage) {
          this._currentPage = null;
        }
      } catch (e) {
        console.warn('清理页面失败:', e);
      }

      try {
        // 清理PDF文档
        if (this._pdfDocument) {
          this._pdfDocument.destroy();
          this._pdfDocument = null;
        }
      } catch (e) {
        console.warn('清理文档失败:', e);
      }
    }
  }
}
</script>

<style scoped>
.pdf-viewer-compat {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.pdf-info {
  font-size: 14px;
  color: #555;
}

.pdf-controls {
  display: flex;
  gap: 8px;
}

.pdf-btn {
  background: none;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  color: #4682B4;
  transition: all 0.2s;
  font-size: 12px;
}

.pdf-btn:hover:not(:disabled) {
  background: #f0f0f0;
  color: #1E90FF;
}

.pdf-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.pdf-container {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  position: relative;
}

.pdf-canvas {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background: white;
  max-width: 100%;
  height: auto;
}

.pdf-loading, .pdf-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}

.pdf-loading i, .pdf-error i {
  font-size: 32px;
  margin-bottom: 15px;
  display: block;
}

.pdf-error i {
  color: #dc3545;
}

.pdf-error h4 {
  margin: 10px 0;
  color: #333;
}

.pdf-error-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  justify-content: center;
}

.pdf-download-btn,
.pdf-retry-btn {
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.pdf-download-btn {
  background: #4682B4;
  color: white;
}

.pdf-download-btn:hover {
  background: #1E90FF;
}

.pdf-retry-btn {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e0e0e0;
}

.pdf-retry-btn:hover {
  background: #e9ecef;
  color: #333;
}

.pdf-error-tip {
  margin-top: 15px;
  color: #999;
  font-size: 12px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .pdf-toolbar {
    padding: 8px 10px;
  }

  .pdf-controls {
    gap: 4px;
  }

  .pdf-btn {
    padding: 4px 8px;
    font-size: 11px;
  }

  .pdf-container {
    padding: 10px;
  }
}
</style>
