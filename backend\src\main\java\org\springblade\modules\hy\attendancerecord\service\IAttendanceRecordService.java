/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.attendancerecord.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.modules.hy.attendancerecord.pojo.entity.AttendanceRecordEntity;
import org.springblade.modules.hy.attendancerecord.pojo.vo.AttendanceRecordVO;
import org.springblade.modules.hy.attendancerecord.excel.AttendanceRecordExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.hy.checkin.pojo.dto.QRCodeResponseDTO;

import java.util.List;

/**
 * 参会签到记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
public interface IAttendanceRecordService extends BaseService<AttendanceRecordEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param attendanceRecord 查询参数
	 * @return IPage<AttendanceRecordVO>
	 */
	IPage<AttendanceRecordVO> selectAttendanceRecordPage(IPage<AttendanceRecordVO> page, AttendanceRecordVO attendanceRecord);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<AttendanceRecordExcel>
	 */
	List<AttendanceRecordExcel> exportAttendanceRecord(Wrapper<AttendanceRecordEntity> queryWrapper);

	/**
	 * 生成议程签到二维码
	 *
	 * @param agendaId 议程ID
	 * @return 二维码响应数据
	 */
	QRCodeResponseDTO generateAgendaQRCode(Long agendaId);

	/**
	 * 议程签到
	 *
	 * @param agendaId 议程ID
	 * @param userId   当前用户ID
	 * @return 签到结果
	 */
	AttendanceRecordEntity checkinByAgenda(Long agendaId, Long userId);

	/**
	 * 查询用户签到状态
	 *
	 * @param userId   用户ID
	 * @param agendaId 议程ID（可选）
	 * @return 签到记录
	 */
	AttendanceRecordEntity getCheckinStatus(Long userId, Long agendaId);

	/**
	 * 检查是否已签到
	 *
	 * @param userId   用户ID
	 * @param agendaId 议程ID（可选）
	 * @return 是否已签到
	 */
	boolean isCheckedIn(Long userId, Long agendaId);

}
