/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hy.userschedule.pojo.entity.UserScheduleEntity;
import org.springblade.modules.hy.userschedule.pojo.vo.UserScheduleVO;
import org.springblade.modules.hy.userschedule.pojo.vo.UserComprehensiveInfoVO;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleExcel;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleImportExcel;
import org.springblade.modules.hy.userschedule.pojo.dto.UserScheduleImportResultDTO;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.hy.hotel.pojo.entity.HotelEntity;
import org.springblade.modules.hy.hotel.service.IHotelService;
import org.springblade.modules.hy.userAgenda.pojo.entity.UserAgendaEntity;
import org.springblade.modules.hy.userAgenda.service.IUserAgendaService;
import org.springblade.modules.hy.dinner.pojo.entity.DinnerEntity;
import org.springblade.modules.hy.dinner.service.IDinnerService;
import org.springblade.common.utils.PinyinUtil;
import org.springblade.core.tool.utils.DigestUtil;
import org.springblade.common.constant.CommonConstant;
import com.alibaba.excel.EasyExcel;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import org.springblade.modules.hy.userschedule.mapper.UserScheduleMapper;
import org.springblade.modules.hy.userschedule.service.IUserScheduleService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;

/**
 * 用户日程信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
@AllArgsConstructor
public class UserScheduleServiceImpl extends BaseServiceImpl<UserScheduleMapper, UserScheduleEntity> implements IUserScheduleService {

	private final IUserService userService;
	private final IHotelService hotelService;
	private final IUserAgendaService userAgendaService;
	private final IDinnerService dinnerService;

	@Override
	public IPage<UserScheduleVO> selectUserSchedulePage(IPage<UserScheduleVO> page, UserScheduleVO userSchedule) {
		return page.setRecords(baseMapper.selectUserSchedulePage(page, userSchedule));
	}

	@Override
	public List<UserScheduleExcel> exportUserSchedule(Wrapper<UserScheduleEntity> queryWrapper) {
		return baseMapper.exportUserSchedule(queryWrapper);
	}

	@Override
	public UserScheduleVO getByUserId(Long userId) {
		return baseMapper.selectByUserId(userId);
	}

	@Override
	public boolean saveOrUpdateByUserId(UserScheduleEntity userSchedule) {
		// 根据用户ID查询是否已存在记录
		LambdaQueryWrapper<UserScheduleEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(UserScheduleEntity::getUserId, userSchedule.getUserId());
		queryWrapper.eq(UserScheduleEntity::getIsDeleted, 0);

		UserScheduleEntity existingSchedule = this.getOne(queryWrapper);

		if (existingSchedule != null) {
			// 更新现有记录
			userSchedule.setId(existingSchedule.getId());
			return this.updateById(userSchedule);
		} else {
			// 创建新记录
			return this.save(userSchedule);
		}
	}

	@Override
	public UserScheduleImportResultDTO importUserSchedule(MultipartFile file) {
		UserScheduleImportResultDTO result = new UserScheduleImportResultDTO();
		List<String> errorMessages = new ArrayList<>();

		try {
			// 读取Excel文件
			List<UserScheduleImportExcel> importList = EasyExcel.read(file.getInputStream())
				.head(UserScheduleImportExcel.class)
				.sheet()
				.doReadSync();

			if (importList == null || importList.isEmpty()) {
				result.setSuccess(false);
				result.setMessage("Excel文件为空或格式不正确");
				return result;
			}

			int totalCount = importList.size();
			int successCount = 0;
			int failCount = 0;
			int updateCount = 0;
			int insertCount = 0;

			// 逐行处理数据
			for (int i = 0; i < importList.size(); i++) {
				UserScheduleImportExcel importData = importList.get(i);
				int rowNum = i + 2; // Excel行号从2开始（第1行是标题）

				try {
					// 数据验证：只验证姓名和手机号不能为空
					if (StringUtil.isBlank(importData.getUserRealName())) {
						errorMessages.add("第" + rowNum + "行：用户姓名不能为空");
						failCount++;
						continue;
					}

					if (StringUtil.isBlank(importData.getUserPhone())) {
						errorMessages.add("第" + rowNum + "行：手机号不能为空");
						failCount++;
						continue;
					}

					// 根据手机号查找或创建用户
					User user = findOrCreateUserByPhone(importData, rowNum, errorMessages);
					if (user == null) {
						failCount++;
						continue;
					}

					// 创建或更新用户日程
					UserScheduleEntity scheduleEntity = new UserScheduleEntity();
					scheduleEntity.setUserId(user.getId());
					// scheduleEntity.setScheduleContent(importData.getScheduleContent());
					// scheduleEntity.setDiningInfo(importData.getDiningInfo());
					// scheduleEntity.setAccommodationInfo(importData.getAccommodationInfo());
					scheduleEntity.setUserRealName(importData.getUserRealName());
					scheduleEntity.setRoomNumber(importData.getRoomNumber());
					scheduleEntity.setMeetingSeatNumber(importData.getMeetingSeatNumber());

					// 检查是否已存在该用户的日程
					LambdaQueryWrapper<UserScheduleEntity> queryWrapper = new LambdaQueryWrapper<>();
					queryWrapper.eq(UserScheduleEntity::getUserId, user.getId());
					queryWrapper.eq(UserScheduleEntity::getIsDeleted, 0);
					UserScheduleEntity existingSchedule = this.getOne(queryWrapper);

					if (existingSchedule != null) {
						// 更新现有记录
						scheduleEntity.setId(existingSchedule.getId());
						this.updateById(scheduleEntity);
						updateCount++;
					} else {
						// 创建新记录
						this.save(scheduleEntity);
						insertCount++;
					}

					successCount++;

				} catch (Exception e) {
					errorMessages.add("第" + rowNum + "行：" + e.getMessage());
					failCount++;
				}
			}

			// 设置结果
			result.setSuccess(failCount == 0);
			result.setTotalCount(totalCount);
			result.setSuccessCount(successCount);
			result.setFailCount(failCount);
			result.setUpdateCount(updateCount);
			result.setInsertCount(insertCount);
			result.setErrorMessages(errorMessages);

			if (failCount == 0) {
				result.setMessage("导入成功！共处理" + successCount + "条数据（新增" + insertCount + "条，更新" + updateCount + "条）");
			} else {
				result.setMessage("导入完成！成功" + successCount + "条，失败" + failCount + "条");
			}

		} catch (Exception e) {
			result.setSuccess(false);
			result.setMessage("导入失败：" + e.getMessage());
			errorMessages.add("文件解析失败：" + e.getMessage());
			result.setErrorMessages(errorMessages);
		}

		return result;
	}

	/**
	 * 根据手机号查找或创建用户
	 */
	private User findOrCreateUserByPhone(UserScheduleImportExcel importData, int rowNum, List<String> errorMessages) {
		try {
			// 先根据手机号查找用户
			LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
			userQuery.eq(User::getPhone, importData.getUserPhone());
			User user = userService.getOne(userQuery);

			boolean userUpdated = false;

			if (user == null) {
				// 用户不存在，创建新用户
				user = new User();
				user.setRealName(importData.getUserRealName());
				user.setName(importData.getUserRealName()); // 昵称默认为真实姓名
				user.setPhone(importData.getUserPhone()); // 设置手机号
				user.setTenantId("000000");
				user.setUserType(1);

				// 生成账号（使用拼音码）
				String username = generateUniqueUsername(importData.getUserRealName());
				user.setAccount(username);

				// 设置默认密码
				user.setPassword(DigestUtil.encrypt(CommonConstant.DEFAULT_PASSWORD));

				// 设置其他用户信息（可以为空）
				if (StrUtil.isNotBlank(importData.getIdNumber())) {
					user.setIdNumber(importData.getIdNumber());
				}
				if (StrUtil.isNotBlank(importData.getSex())) {
					user.setSex(importData.getSex().equals("男") ? 1 : 2);
				}
				if (StrUtil.isNotBlank(importData.getUserEmployeeNumber())) {
					user.setEmployeeNumber(importData.getUserEmployeeNumber());
				}
				if (StrUtil.isNotBlank(importData.getWorkUnit())) {
					user.setWorkUnit(importData.getWorkUnit());
				}
				if (StrUtil.isNotBlank(importData.getPosition())) {
					user.setPosition(importData.getPosition());
				}

				// 注意：房号和会议座位号现在使用UserSchedule中的字段，不再设置User表

				userService.saveOrUpdate(user);
			} else {
				// 用户存在，更新用户信息（姓名和其他信息）
				if (StringUtil.isNotBlank(importData.getUserRealName()) &&
					!importData.getUserRealName().equals(user.getRealName())) {
					user.setRealName(importData.getUserRealName());
					user.setName(importData.getUserRealName()); // 同时更新昵称
					userUpdated = true;
				}

				if (StringUtil.isNotBlank(importData.getUserEmployeeNumber()) &&
					!importData.getUserEmployeeNumber().equals(user.getEmployeeNumber())) {
					user.setEmployeeNumber(importData.getUserEmployeeNumber());
					userUpdated = true;
				}

				// 设置其他用户信息（可以为空）
				if (StrUtil.isNotBlank(importData.getIdNumber()) &&
					!importData.getIdNumber().equals(user.getIdNumber())) {
					user.setIdNumber(importData.getIdNumber());
					userUpdated = true;
				}
				if (StrUtil.isNotBlank(importData.getUserEmployeeNumber()) &&
					!importData.getUserEmployeeNumber().equals(user.getEmployeeNumber())) {
					user.setEmployeeNumber(importData.getUserEmployeeNumber());
					userUpdated = true;
				}
				if (StrUtil.isNotBlank(importData.getWorkUnit()) &&
					!importData.getWorkUnit().equals(user.getWorkUnit())) {
					user.setWorkUnit(importData.getWorkUnit());
					userUpdated = true;
				}
				if (StrUtil.isNotBlank(importData.getPosition()) &&
					!importData.getPosition().equals(user.getPosition())) {
					user.setPosition(importData.getPosition());
					userUpdated = true;
				}

				// 注意：房号和会议座位号现在使用UserSchedule中的字段，不再更新User表

				if (userUpdated) {
					userService.updateById(user);
				}
			}

			return user;
		} catch (Exception e) {
			errorMessages.add("第" + rowNum + "行：用户信息处理失败 - " + e.getMessage());
			return null;
		}
	}

	@Override
	public List<UserScheduleImportExcel> getImportTemplate() {
		// 返回模板数据，包含示例行
		List<UserScheduleImportExcel> templateList = new ArrayList<>();

		UserScheduleImportExcel example = new UserScheduleImportExcel();
		example.setUserRealName("张三");
		example.setIdNumber("123456789012345678");
		example.setUserPhone("13800138000");
		example.setSex("男");
		example.setUserEmployeeNumber("EMP001");
		example.setWorkUnit("某公司");
		example.setPosition("工程师");
		example.setRoomNumber("A101");
		example.setMeetingSeatNumber("A-01");
//		example.setScheduleContent("我的会议日程<p><strong>第一天（2024-07-29）</strong></p><ul><li>09:00-10:30 开幕式及主旨演讲</li><li>10:45-12:00 技术分享会</li><li>14:00-15:30 圆桌讨论</li><li>16:00-17:00 闭幕式</li></ul>");
//		example.setDiningInfo("");
//		example.setAccommodationInfo("");

		templateList.add(example);
		return templateList;
	}

	/**
	 * 生成唯一的用户名
	 *
	 * @param realName 真实姓名
	 * @return 唯一的用户名
	 */
	private String generateUniqueUsername(String realName) {
		// 获取完整拼音作为基础用户名
		String baseUsername = PinyinUtil.generateBaseUsername(realName);

		// 先检查基础用户名是否可用
		if (!isUsernameExists(baseUsername)) {
			return baseUsername;
		}

		// 如果重复，从1开始追加数字
		int suffix = 1;
		String username;
		do {
			username = baseUsername + suffix;
			suffix++;
		} while (isUsernameExists(username));

		return username;
	}

	/**
	 * 检查用户名是否已存在
	 *
	 * @param username 用户名
	 * @return 是否存在
	 */
	private boolean isUsernameExists(String username) {
		// 使用userByAccount方法查询用户
		User existingUser = userService.userByAccount("000000", username);
		return existingUser != null;
	}

	@Override
	public UserComprehensiveInfoVO getUserComprehensiveInfo(Long userId) {
		UserComprehensiveInfoVO result = new UserComprehensiveInfoVO();

		// 1. 查询用户日程信息
		UserScheduleVO userSchedule = this.getByUserId(userId);
		result.setUserSchedule(userSchedule);

		// 2. 根据hotel_id查询酒店信息 (如果有)
		HotelEntity hotel = null;
		if (userSchedule != null && userSchedule.getHotelId() != null) {
			hotel = hotelService.getById(userSchedule.getHotelId());
		}
		result.setHotel(hotel);

		// 3. 查询用户议程列表
		LambdaQueryWrapper<UserAgendaEntity> agendaQuery = new LambdaQueryWrapper<>();
		agendaQuery.eq(UserAgendaEntity::getUserId, userId)
				   .eq(UserAgendaEntity::getIsDeleted, 0)
				   .orderByAsc(UserAgendaEntity::getAgendaDate, UserAgendaEntity::getStartTime);
		List<UserAgendaEntity> agendaList = userAgendaService.list(agendaQuery);
		result.setAgendaList(agendaList);

		// 4. 查询用户用餐记录列表
		LambdaQueryWrapper<DinnerEntity> dinnerQuery = new LambdaQueryWrapper<>();
		dinnerQuery.eq(DinnerEntity::getUserId, userId)
				   .eq(DinnerEntity::getIsDeleted, 0)
				   .orderByDesc(DinnerEntity::getDinnerDate);
		List<DinnerEntity> dinnerList = dinnerService.list(dinnerQuery);
		result.setDinnerList(dinnerList);

		return result;
	}

	@Override
	public IPage<UserComprehensiveInfoVO> selectUserComprehensiveInfoPage(IPage<UserComprehensiveInfoVO> page, UserScheduleVO userSchedule) {
		// 1. 创建UserScheduleVO的分页对象
		IPage<UserScheduleVO> userSchedulePage = new Page<>(page.getCurrent(), page.getSize());
		userSchedulePage = this.selectUserSchedulePage(userSchedulePage, userSchedule);

		// 2. 为每个用户日程构建综合信息
		List<UserComprehensiveInfoVO> comprehensiveInfoList = new ArrayList<>();

		for (UserScheduleVO scheduleVO : userSchedulePage.getRecords()) {
			UserComprehensiveInfoVO comprehensiveInfo = new UserComprehensiveInfoVO();

			// 设置用户日程信息
			comprehensiveInfo.setUserSchedule(scheduleVO);

			// 查询酒店信息
			HotelEntity hotel = null;
			if (scheduleVO.getHotelId() != null) {
				hotel = hotelService.getById(scheduleVO.getHotelId());
			}
			comprehensiveInfo.setHotel(hotel);

			// 查询用户议程列表
			LambdaQueryWrapper<UserAgendaEntity> agendaQuery = new LambdaQueryWrapper<>();
			agendaQuery.eq(UserAgendaEntity::getUserId, scheduleVO.getUserId())
					   .eq(UserAgendaEntity::getIsDeleted, 0)
					   .orderByAsc(UserAgendaEntity::getAgendaDate, UserAgendaEntity::getStartTime);
			List<UserAgendaEntity> agendaList = userAgendaService.list(agendaQuery);
			comprehensiveInfo.setAgendaList(agendaList);

			// 查询用户用餐记录列表
			LambdaQueryWrapper<DinnerEntity> dinnerQuery = new LambdaQueryWrapper<>();
			dinnerQuery.eq(DinnerEntity::getUserId, scheduleVO.getUserId())
					   .eq(DinnerEntity::getIsDeleted, 0)
					   .orderByDesc(DinnerEntity::getDinnerDate);
			List<DinnerEntity> dinnerList = dinnerService.list(dinnerQuery);
			comprehensiveInfo.setDinnerList(dinnerList);

			comprehensiveInfoList.add(comprehensiveInfo);
		}

		// 3. 构建返回的分页结果
		IPage<UserComprehensiveInfoVO> result = page.setRecords(comprehensiveInfoList);
		result.setTotal(userSchedulePage.getTotal());
		result.setCurrent(userSchedulePage.getCurrent());
		result.setSize(userSchedulePage.getSize());
		result.setPages(userSchedulePage.getPages());

		return result;
	}

}
